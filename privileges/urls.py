from django.urls import path
from . import views

app_name = 'privileges'

urlpatterns = [
    # 页面路由
    path('', views.privilege_management, name='management'),

    # API路由
    path('api/list/', views.privilege_list_api, name='api_list'),
    path('api/stats/', views.privilege_stats_api, name='api_stats'),
    path('api/<int:privilege_id>/', views.privilege_detail_api, name='api_detail'),
    path('api/<int:privilege_id>/toggle/', views.privilege_toggle_status, name='api_toggle'),
    path('api/debug/', views.debug_api, name='api_debug'),

    # 特权管理API
    path('api/create/', views.privilege_create_api, name='api_create'),
    path('api/<int:privilege_id>/update/', views.privilege_update_api, name='api_update'),
    path('api/<int:privilege_id>/delete/', views.privilege_delete_api, name='api_delete'),
    path('api/form-data/', views.privilege_form_data_api, name='api_form_data'),
]
