from django.core.management.base import BaseCommand
from privileges.models import VIPLevel, PrivilegeCategory, Privilege, PrivilegeStats
import random


class Command(BaseCommand):
    help = '初始化特权管理系统的示例数据'

    def handle(self, *args, **options):
        self.stdout.write('开始初始化特权数据...')
        
        # 创建VIP等级
        self.create_vip_levels()
        
        # 创建特权分类
        self.create_privilege_categories()
        
        # 创建特权
        self.create_privileges()
        
        # 创建统计数据
        self.create_privilege_stats()
        
        self.stdout.write(
            self.style.SUCCESS('特权数据初始化完成！')
        )

    def create_vip_levels(self):
        """创建VIP等级"""
        vip_levels = [
            {'level': 0, 'name': '普通用户', 'threshold': 0},
            {'level': 1, 'name': 'VIP1', 'threshold': 100},
            {'level': 2, 'name': 'VIP2', 'threshold': 500},
            {'level': 3, 'name': 'VIP3', 'threshold': 1000},
            {'level': 4, 'name': 'VIP4', 'threshold': 2000},
            {'level': 5, 'name': 'VIP5', 'threshold': 5000},
            {'level': 6, 'name': 'VIP6', 'threshold': 10000},
            {'level': 7, 'name': 'VIP7', 'threshold': 20000},
            {'level': 8, 'name': 'VIP8', 'threshold': 50000},
            {'level': 9, 'name': 'VIP9', 'threshold': 100000},
            {'level': 10, 'name': 'VIP10', 'threshold': 200000},
        ]
        
        for vip_data in vip_levels:
            VIPLevel.objects.get_or_create(
                level=vip_data['level'],
                defaults={
                    'name': vip_data['name'],
                    'threshold': vip_data['threshold'],
                    'description': f'{vip_data["name"]}等级，充值门槛{vip_data["threshold"]}元'
                }
            )
        
        self.stdout.write('VIP等级创建完成')

    def create_privilege_categories(self):
        """创建特权分类"""
        categories = [
            {
                'name': 'item_reward',
                'display_name': '道具奖励',
                'icon': 'bi-gift',
                'color': '#10b981',
                'sort_order': 1
            },
            {
                'name': 'coin_reward',
                'display_name': '金币奖励',
                'icon': 'bi-percent',
                'color': '#f59e0b',
                'sort_order': 2
            },
            {
                'name': 'attribute_bonus',
                'display_name': '属性加成',
                'icon': 'bi-speedometer2',
                'color': '#3b82f6',
                'sort_order': 3
            },
            {
                'name': 'identity_mark',
                'display_name': '身份标识',
                'icon': 'bi-star',
                'color': '#8b5cf6',
                'sort_order': 4
            },
            {
                'name': 'security_protection',
                'display_name': '安全保障',
                'icon': 'bi-shield-check',
                'color': '#6366f1',
                'sort_order': 5
            },
        ]
        
        for cat_data in categories:
            PrivilegeCategory.objects.get_or_create(
                name=cat_data['name'],
                defaults=cat_data
            )
        
        self.stdout.write('特权分类创建完成')

    def create_privileges(self):
        """创建特权"""
        # 获取分类和VIP等级
        categories = {cat.name: cat for cat in PrivilegeCategory.objects.all()}
        vip_levels = {level.level: level for level in VIPLevel.objects.all()}
        
        privileges = [
            {
                'name': '专属礼包',
                'category': categories['item_reward'],
                'description': 'VIP用户可获得每日专属礼包，包含稀有道具和资源，礼包内容根据VIP等级动态调整',
                'min_vip_level': vip_levels[1],
                'status': 'active',
                'parameters': {'daily_limit': 1, 'items': ['金币', '经验药水', '强化石']},
                'active_users': 834,
                'usage_count': 2502,
                'sort_order': 1
            },
            {
                'name': '金币加成',
                'category': categories['coin_reward'],
                'description': 'VIP用户获得额外的金币收益加成，加成比例根据VIP等级递增，最高可达200%',
                'min_vip_level': vip_levels[1],
                'status': 'active',
                'parameters': {'base_bonus': 0.2, 'max_bonus': 2.0, 'level_increment': 0.2},
                'active_users': 1156,
                'usage_count': 15678,
                'sort_order': 2
            },
            {
                'name': '经验加速',
                'category': categories['attribute_bonus'],
                'description': 'VIP用户获得经验值获取加速效果，提升角色升级速度，享受更快的成长体验',
                'min_vip_level': vip_levels[2],
                'status': 'active',
                'parameters': {'exp_multiplier': 1.5, 'max_multiplier': 3.0},
                'active_users': 967,
                'usage_count': 8934,
                'sort_order': 3
            },
            {
                'name': '专属称号',
                'category': categories['identity_mark'],
                'description': 'VIP用户可获得专属称号和特殊标识，在游戏中展示尊贵身份，彰显独特地位',
                'min_vip_level': vip_levels[3],
                'status': 'active',
                'parameters': {'title_color': 'gold', 'special_effects': True},
                'active_users': 723,
                'usage_count': 723,
                'sort_order': 4
            },
            {
                'name': '账号保护',
                'category': categories['security_protection'],
                'description': 'VIP用户享受优先的账号安全保护服务，包括异常登录提醒、资产保护等安全功能',
                'min_vip_level': vip_levels[1],
                'status': 'active',
                'parameters': {'login_protection': True, 'asset_protection': True, 'priority_support': True},
                'active_users': 1246,
                'usage_count': 3456,
                'sort_order': 5
            },
            {
                'name': '双倍掉落',
                'category': categories['item_reward'],
                'description': 'VIP用户获得双倍物品掉落率，提升装备和道具的获取效率（当前已禁用）',
                'min_vip_level': vip_levels[4],
                'status': 'disabled',
                'parameters': {'drop_multiplier': 2.0, 'applicable_items': ['装备', '道具', '材料']},
                'active_users': 0,
                'usage_count': 0,
                'sort_order': 6
            },
        ]
        
        for priv_data in privileges:
            Privilege.objects.get_or_create(
                name=priv_data['name'],
                defaults=priv_data
            )
        
        self.stdout.write('特权创建完成')

    def create_privilege_stats(self):
        """创建特权统计数据"""
        for privilege in Privilege.objects.all():
            stats, created = PrivilegeStats.objects.get_or_create(
                privilege=privilege,
                defaults={
                    'total_users': privilege.active_users,
                    'active_users_today': random.randint(10, 50),
                    'active_users_week': random.randint(50, 200),
                    'active_users_month': privilege.active_users,
                    'total_usage': privilege.usage_count,
                    'avg_usage_per_user': privilege.usage_count / max(privilege.active_users, 1),
                    'popularity_score': random.uniform(1.0, 10.0),
                }
            )
            if created:
                self.stdout.write(f'为特权 {privilege.name} 创建统计数据')
        
        self.stdout.write('特权统计数据创建完成')
