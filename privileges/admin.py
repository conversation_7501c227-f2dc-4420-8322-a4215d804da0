from django.contrib import admin
from .models import VIPLevel, PrivilegeCategory, Privilege, PrivilegeUsageLog, PrivilegeStats


@admin.register(VIPLevel)
class VIPLevelAdmin(admin.ModelAdmin):
    list_display = ['level', 'name', 'threshold', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name']
    ordering = ['level']


@admin.register(PrivilegeCategory)
class PrivilegeCategoryAdmin(admin.ModelAdmin):
    list_display = ['display_name', 'name', 'icon', 'color', 'sort_order']
    list_filter = ['name']
    search_fields = ['display_name', 'name']
    ordering = ['sort_order', 'name']


@admin.register(Privilege)
class PrivilegeAdmin(admin.ModelAdmin):
    list_display = ['name', 'category', 'status', 'min_vip_level', 'active_users', 'usage_count', 'created_at']
    list_filter = ['status', 'category', 'min_vip_level', 'is_featured', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['sort_order', '-created_at']
    readonly_fields = ['usage_count', 'active_users', 'created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'category', 'description', 'status')
        }),
        ('VIP要求', {
            'fields': ('min_vip_level',)
        }),
        ('参数配置', {
            'fields': ('parameters',),
            'classes': ('collapse',)
        }),
        ('显示设置', {
            'fields': ('sort_order', 'is_featured')
        }),
        ('统计信息', {
            'fields': ('usage_count', 'active_users'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(PrivilegeUsageLog)
class PrivilegeUsageLogAdmin(admin.ModelAdmin):
    list_display = ['privilege', 'user_id', 'usage_date', 'usage_count', 'created_at']
    list_filter = ['usage_date', 'privilege__category', 'created_at']
    search_fields = ['privilege__name', 'user_id']
    ordering = ['-usage_date', '-created_at']
    readonly_fields = ['created_at']


@admin.register(PrivilegeStats)
class PrivilegeStatsAdmin(admin.ModelAdmin):
    list_display = ['privilege', 'total_users', 'active_users_today', 'total_usage', 'popularity_score', 'last_updated']
    list_filter = ['last_updated']
    search_fields = ['privilege__name']
    ordering = ['-popularity_score', '-total_users']
    readonly_fields = ['last_updated']
