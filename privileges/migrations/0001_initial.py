# Generated by Django 5.2.4 on 2025-08-05 06:45

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PrivilegeCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('item_reward', '道具奖励'), ('coin_reward', '金币奖励'), ('attribute_bonus', '属性加成'), ('identity_mark', '身份标识'), ('security_protection', '安全保障')], max_length=20, unique=True, verbose_name='分类名称')),
                ('display_name', models.CharField(max_length=50, verbose_name='显示名称')),
                ('icon', models.Char<PERSON>ield(max_length=50, verbose_name='图标类名')),
                ('color', models.CharField(default='#3b82f6', max_length=7, verbose_name='主题色')),
                ('description', models.TextField(blank=True, verbose_name='分类描述')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
            ],
            options={
                'verbose_name': '特权分类',
                'verbose_name_plural': '特权分类',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='VIPLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.IntegerField(unique=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(10)], verbose_name='VIP等级')),
                ('name', models.CharField(max_length=50, verbose_name='等级名称')),
                ('threshold', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='充值门槛')),
                ('description', models.TextField(blank=True, verbose_name='等级描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': 'VIP等级',
                'verbose_name_plural': 'VIP等级',
                'ordering': ['level'],
            },
        ),
        migrations.CreateModel(
            name='Privilege',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='特权名称')),
                ('description', models.TextField(verbose_name='特权描述')),
                ('status', models.CharField(choices=[('active', '已启用'), ('disabled', '已禁用'), ('maintenance', '维护中')], default='active', max_length=20, verbose_name='状态')),
                ('parameters', models.JSONField(blank=True, default=dict, help_text='存储特权的具体参数配置', verbose_name='特权参数')),
                ('usage_count', models.IntegerField(default=0, verbose_name='使用次数')),
                ('active_users', models.IntegerField(default=0, verbose_name='活跃用户数')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('is_featured', models.BooleanField(default=False, verbose_name='是否推荐')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='privileges.privilegecategory', verbose_name='特权分类')),
                ('min_vip_level', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='privileges.viplevel', verbose_name='最低VIP等级要求')),
            ],
            options={
                'verbose_name': '特权',
                'verbose_name_plural': '特权',
                'ordering': ['sort_order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PrivilegeStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_users', models.IntegerField(default=0, verbose_name='总用户数')),
                ('active_users_today', models.IntegerField(default=0, verbose_name='今日活跃用户')),
                ('active_users_week', models.IntegerField(default=0, verbose_name='本周活跃用户')),
                ('active_users_month', models.IntegerField(default=0, verbose_name='本月活跃用户')),
                ('total_usage', models.IntegerField(default=0, verbose_name='总使用次数')),
                ('avg_usage_per_user', models.FloatField(default=0.0, verbose_name='人均使用次数')),
                ('popularity_score', models.FloatField(default=0.0, verbose_name='受欢迎度评分')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='最后更新时间')),
                ('privilege', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='privileges.privilege', verbose_name='特权')),
            ],
            options={
                'verbose_name': '特权统计',
                'verbose_name_plural': '特权统计',
            },
        ),
        migrations.CreateModel(
            name='PrivilegeUsageLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.IntegerField(verbose_name='用户ID')),
                ('usage_date', models.DateField(auto_now_add=True, verbose_name='使用日期')),
                ('usage_count', models.IntegerField(default=1, verbose_name='使用次数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('privilege', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='privileges.privilege', verbose_name='特权')),
            ],
            options={
                'verbose_name': '特权使用记录',
                'verbose_name_plural': '特权使用记录',
                'indexes': [models.Index(fields=['privilege', 'usage_date'], name='privileges__privile_a9fca0_idx'), models.Index(fields=['user_id', 'usage_date'], name='privileges__user_id_a7bf99_idx')],
                'unique_together': {('privilege', 'user_id', 'usage_date')},
            },
        ),
        migrations.AddIndex(
            model_name='privilege',
            index=models.Index(fields=['status'], name='privileges__status_6bc363_idx'),
        ),
        migrations.AddIndex(
            model_name='privilege',
            index=models.Index(fields=['category'], name='privileges__categor_2f46bd_idx'),
        ),
        migrations.AddIndex(
            model_name='privilege',
            index=models.Index(fields=['min_vip_level'], name='privileges__min_vip_438189_idx'),
        ),
    ]
