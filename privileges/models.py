from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator


class VIPLevel(models.Model):
    """VIP等级模型"""
    level = models.IntegerField(
        unique=True,
        validators=[MinValueValidator(0), MaxValueValidator(10)],
        verbose_name="VIP等级"
    )
    name = models.CharField(max_length=50, verbose_name="等级名称")
    threshold = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name="充值门槛"
    )
    description = models.TextField(blank=True, verbose_name="等级描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "VIP等级"
        verbose_name_plural = "VIP等级"
        ordering = ['level']

    def __str__(self):
        return f"VIP{self.level} - {self.name}"


class PrivilegeCategory(models.Model):
    """特权分类模型"""
    CATEGORY_CHOICES = [
        ('item_reward', '道具奖励'),
        ('coin_reward', '金币奖励'),
        ('attribute_bonus', '属性加成'),
        ('identity_mark', '身份标识'),
        ('security_protection', '安全保障'),
    ]

    name = models.CharField(
        max_length=20,
        choices=CATEGORY_CHOICES,
        unique=True,
        verbose_name="分类名称"
    )
    display_name = models.CharField(max_length=50, verbose_name="显示名称")
    icon = models.CharField(max_length=50, verbose_name="图标类名")
    color = models.CharField(max_length=7, default="#3b82f6", verbose_name="主题色")
    description = models.TextField(blank=True, verbose_name="分类描述")
    sort_order = models.IntegerField(default=0, verbose_name="排序")

    class Meta:
        verbose_name = "特权分类"
        verbose_name_plural = "特权分类"
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.display_name


class Privilege(models.Model):
    """特权模型"""
    STATUS_CHOICES = [
        ('active', '已启用'),
        ('disabled', '已禁用'),
        ('maintenance', '维护中'),
    ]

    name = models.CharField(max_length=100, verbose_name="特权名称")
    category = models.ForeignKey(
        PrivilegeCategory,
        on_delete=models.CASCADE,
        verbose_name="特权分类"
    )
    description = models.TextField(verbose_name="特权描述")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        verbose_name="状态"
    )

    # VIP等级要求
    min_vip_level = models.ForeignKey(
        VIPLevel,
        on_delete=models.CASCADE,
        verbose_name="最低VIP等级要求"
    )



    # 使用统计
    usage_count = models.IntegerField(default=0, verbose_name="使用次数")
    active_users = models.IntegerField(default=0, verbose_name="活跃用户数")

    # 排序和显示
    sort_order = models.IntegerField(default=0, verbose_name="排序")
    is_featured = models.BooleanField(default=False, verbose_name="是否推荐")

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "特权"
        verbose_name_plural = "特权"
        ordering = ['sort_order', '-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['category']),
            models.Index(fields=['min_vip_level']),
        ]

    def __str__(self):
        return self.name

    @property
    def real_active_users(self):
        """从特权处理记录中统计真实的使用人数"""
        try:
            from service_analysis.models import PrivilegeProcessRecord
            return PrivilegeProcessRecord.objects.filter(
                privilege=self
            ).values('character_id').distinct().count()
        except ImportError:
            # 如果service_analysis应用未安装，返回原始值
            return self.active_users

    @property
    def real_usage_count(self):
        """从特权处理记录中统计真实的使用次数"""
        try:
            from service_analysis.models import PrivilegeProcessRecord
            return PrivilegeProcessRecord.objects.filter(privilege=self).count()
        except ImportError:
            # 如果service_analysis应用未安装，返回原始值
            return self.usage_count

    @property
    def is_active(self):
        """判断特权是否激活"""
        return self.status == 'active'

    def toggle_status(self):
        """切换特权状态"""
        if self.status == 'active':
            self.status = 'disabled'
        else:
            self.status = 'active'
        self.save()
        return self.status


class PrivilegeUsageLog(models.Model):
    """特权使用记录模型"""
    privilege = models.ForeignKey(
        Privilege,
        on_delete=models.CASCADE,
        verbose_name="特权"
    )
    user_id = models.IntegerField(verbose_name="用户ID")
    usage_date = models.DateField(auto_now_add=True, verbose_name="使用日期")
    usage_count = models.IntegerField(default=1, verbose_name="使用次数")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "特权使用记录"
        verbose_name_plural = "特权使用记录"
        unique_together = ['privilege', 'user_id', 'usage_date']
        indexes = [
            models.Index(fields=['privilege', 'usage_date']),
            models.Index(fields=['user_id', 'usage_date']),
        ]

    def __str__(self):
        return f"{self.privilege.name} - 用户{self.user_id} - {self.usage_date}"


class PrivilegeStats(models.Model):
    """特权统计模型"""
    privilege = models.OneToOneField(
        Privilege,
        on_delete=models.CASCADE,
        verbose_name="特权"
    )
    total_users = models.IntegerField(default=0, verbose_name="总用户数")
    active_users_today = models.IntegerField(default=0, verbose_name="今日活跃用户")
    active_users_week = models.IntegerField(default=0, verbose_name="本周活跃用户")
    active_users_month = models.IntegerField(default=0, verbose_name="本月活跃用户")
    total_usage = models.IntegerField(default=0, verbose_name="总使用次数")
    avg_usage_per_user = models.FloatField(default=0.0, verbose_name="人均使用次数")
    popularity_score = models.FloatField(default=0.0, verbose_name="受欢迎度评分")
    last_updated = models.DateTimeField(auto_now=True, verbose_name="最后更新时间")

    class Meta:
        verbose_name = "特权统计"
        verbose_name_plural = "特权统计"

    def __str__(self):
        return f"{self.privilege.name} - 统计数据"

    def update_stats(self):
        """更新统计数据"""
        from django.db.models import Count, Sum
        from datetime import date, timedelta

        today = date.today()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        # 计算各时间段的活跃用户数
        usage_logs = PrivilegeUsageLog.objects.filter(privilege=self.privilege)

        self.active_users_today = usage_logs.filter(
            usage_date=today
        ).values('user_id').distinct().count()

        self.active_users_week = usage_logs.filter(
            usage_date__gte=week_ago
        ).values('user_id').distinct().count()

        self.active_users_month = usage_logs.filter(
            usage_date__gte=month_ago
        ).values('user_id').distinct().count()

        self.total_users = usage_logs.values('user_id').distinct().count()
        self.total_usage = usage_logs.aggregate(
            total=Sum('usage_count')
        )['total'] or 0

        if self.total_users > 0:
            self.avg_usage_per_user = self.total_usage / self.total_users

        # 计算受欢迎度评分 (基于活跃用户比例和使用频次)
        if self.total_users > 0:
            active_ratio = self.active_users_week / self.total_users
            self.popularity_score = active_ratio * self.avg_usage_per_user * 10

        self.save()
