from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Count, Q
from django.core.paginator import Paginator
import json

from .models import Privilege, PrivilegeCategory, VIPLevel, PrivilegeStats


def privilege_management(request):
    """特权管理页面视图"""
    # 获取统计数据
    total_privileges = Privilege.objects.count()
    active_privileges = Privilege.objects.filter(status='active').count()
    disabled_privileges = Privilege.objects.filter(status='disabled').count()

    # 计算总活跃用户数（从特权处理记录中统计）
    total_active_users = sum(
        privilege.real_active_users for privilege in Privilege.objects.filter(status='active')
    )

    context = {
        'page_title': '特权管理',
        'page_description': 'VIP特权项目的配置和管理中心',
        'stats': {
            'total_privileges': total_privileges,
            'active_privileges': active_privileges,
            'disabled_privileges': disabled_privileges,
            'total_active_users': total_active_users,
        }
    }
    return render(request, 'privileges/privilege_management.html', context)


def privilege_list_api(request):
    """特权列表API"""
    # 获取查询参数
    search = request.GET.get('search', '').strip()
    category = request.GET.get('category', '')
    status = request.GET.get('status', '')
    page = int(request.GET.get('page', 1))
    page_size = int(request.GET.get('page_size', 20))

    # 构建查询
    queryset = Privilege.objects.select_related('category', 'min_vip_level')

    if search:
        queryset = queryset.filter(
            Q(name__icontains=search) |
            Q(description__icontains=search) |
            Q(category__display_name__icontains=search)
        )

    if category:
        queryset = queryset.filter(category__name=category)

    if status:
        queryset = queryset.filter(status=status)

    # 分页
    paginator = Paginator(queryset, page_size)
    page_obj = paginator.get_page(page)

    # 序列化数据
    privileges_data = []
    for privilege in page_obj:
        privileges_data.append({
            'id': privilege.id,
            'name': privilege.name,
            'category': {
                'name': privilege.category.name,
                'display_name': privilege.category.display_name,
                'icon': privilege.category.icon,
                'color': privilege.category.color,
            },
            'description': privilege.description,
            'status': privilege.status,
            'status_display': privilege.get_status_display(),
            'min_vip_level': {
                'level': privilege.min_vip_level.level,
                'name': privilege.min_vip_level.name,
            },
            'active_users': privilege.real_active_users,
            'usage_count': privilege.real_usage_count,
            'is_featured': privilege.is_featured,
            'created_at': privilege.created_at.isoformat(),
            'updated_at': privilege.updated_at.isoformat(),
        })

    return JsonResponse({
        'success': True,
        'data': {
            'privileges': privileges_data,
            'pagination': {
                'current_page': page_obj.number,
                'total_pages': paginator.num_pages,
                'total_count': paginator.count,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            }
        }
    })


@csrf_exempt
@require_http_methods(["POST"])
def privilege_toggle_status(request, privilege_id):
    """切换特权状态API"""
    try:
        privilege = get_object_or_404(Privilege, id=privilege_id)
        old_status = privilege.status
        new_status = privilege.toggle_status()

        return JsonResponse({
            'success': True,
            'message': f'特权状态已从"{privilege.get_status_display()}"切换为"{privilege.get_status_display()}"',
            'data': {
                'id': privilege.id,
                'name': privilege.name,
                'old_status': old_status,
                'new_status': new_status,
                'status_display': privilege.get_status_display(),
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'操作失败: {str(e)}'
        }, status=400)


def privilege_stats_api(request):
    """特权统计数据API"""
    try:
        # 基础统计
        total_privileges = Privilege.objects.count()
        active_privileges = Privilege.objects.filter(status='active').count()
        disabled_privileges = Privilege.objects.filter(status='disabled').count()
        maintenance_privileges = Privilege.objects.filter(status='maintenance').count()

        # 计算总活跃用户数（从特权处理记录中统计）
        total_active_users = sum(
            privilege.real_active_users for privilege in Privilege.objects.filter(status='active')
        )

        # 分类统计
        category_stats = []
        for category in PrivilegeCategory.objects.all():
            category_privilege_count = Privilege.objects.filter(category=category).count()
            category_active_count = Privilege.objects.filter(
                category=category, status='active'
            ).count()

            category_stats.append({
                'name': category.name,
                'display_name': category.display_name,
                'icon': category.icon,
                'color': category.color,
                'total_privileges': category_privilege_count,
                'active_privileges': category_active_count,
            })

        # VIP等级统计
        vip_stats = []
        for vip_level in VIPLevel.objects.all():
            vip_privilege_count = Privilege.objects.filter(min_vip_level=vip_level).count()
            vip_stats.append({
                'level': vip_level.level,
                'name': vip_level.name,
                'privilege_count': vip_privilege_count,
            })

        return JsonResponse({
            'success': True,
            'data': {
                'overview': {
                    'total_privileges': total_privileges,
                    'active_privileges': active_privileges,
                    'disabled_privileges': disabled_privileges,
                    'maintenance_privileges': maintenance_privileges,
                    'total_active_users': total_active_users,
                    'active_rate': round(active_privileges / total_privileges * 100, 1) if total_privileges > 0 else 0,
                },
                'category_stats': category_stats,
                'vip_stats': vip_stats,
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取统计数据失败: {str(e)}'
        }, status=500)


def privilege_detail_api(request, privilege_id):
    """特权详情API"""
    try:
        privilege = get_object_or_404(
            Privilege.objects.select_related('category', 'min_vip_level'),
            id=privilege_id
        )

        # 获取统计数据
        try:
            stats = PrivilegeStats.objects.get(privilege=privilege)
            stats_data = {
                'total_users': stats.total_users,
                'active_users_today': stats.active_users_today,
                'active_users_week': stats.active_users_week,
                'active_users_month': stats.active_users_month,
                'total_usage': stats.total_usage,
                'avg_usage_per_user': round(stats.avg_usage_per_user, 2),
                'popularity_score': round(stats.popularity_score, 2),
                'last_updated': stats.last_updated.isoformat(),
            }
        except PrivilegeStats.DoesNotExist:
            stats_data = None

        return JsonResponse({
            'success': True,
            'data': {
                'id': privilege.id,
                'name': privilege.name,
                'category': {
                    'name': privilege.category.name,
                    'display_name': privilege.category.display_name,
                    'icon': privilege.category.icon,
                    'color': privilege.category.color,
                },
                'description': privilege.description,
                'status': privilege.status,
                'status_display': privilege.get_status_display(),
                'min_vip_level': {
                    'level': privilege.min_vip_level.level,
                    'name': privilege.min_vip_level.name,
                    'threshold': str(privilege.min_vip_level.threshold),
                },

                'active_users': privilege.real_active_users,
                'usage_count': privilege.real_usage_count,
                'is_featured': privilege.is_featured,
                'sort_order': privilege.sort_order,
                'created_at': privilege.created_at.isoformat(),
                'updated_at': privilege.updated_at.isoformat(),
                'stats': stats_data,
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取特权详情失败: {str(e)}'
        }, status=500)


def debug_api(request):
    """调试API - 检查前后端连接"""
    from django.utils import timezone

    try:
        # 基础统计
        total_privileges = Privilege.objects.count()
        active_privileges = Privilege.objects.filter(status='active').count()

        # 获取前5个特权
        privileges = Privilege.objects.select_related('category', 'min_vip_level')[:5]
        privilege_list = []

        for privilege in privileges:
            privilege_list.append({
                'id': privilege.id,
                'name': privilege.name,
                'status': privilege.status,
                'category': privilege.category.display_name,
                'active_users': privilege.real_active_users,
            })

        debug_info = {
            'timestamp': timezone.now().isoformat(),
            'database_status': 'connected',
            'total_privileges': total_privileges,
            'active_privileges': active_privileges,
            'sample_privileges': privilege_list,
            'api_endpoints': [
                '/privileges/api/list/',
                '/privileges/api/stats/',
                '/privileges/api/{id}/',
                '/privileges/api/{id}/toggle/',
            ]
        }

        return JsonResponse({
            'success': True,
            'message': '前后端连接正常',
            'debug_info': debug_info
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'调试失败: {str(e)}',
            'error_type': type(e).__name__
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def privilege_create_api(request):
    """创建特权API"""
    try:
        data = json.loads(request.body)

        # 验证必填字段
        required_fields = ['name', 'category_id', 'description', 'min_vip_level_id']
        for field in required_fields:
            if not data.get(field):
                return JsonResponse({
                    'success': False,
                    'message': f'缺少必填字段: {field}'
                }, status=400)

        # 检查分类和VIP等级是否存在
        try:
            category = PrivilegeCategory.objects.get(id=data['category_id'])
            vip_level = VIPLevel.objects.get(id=data['min_vip_level_id'])
        except (PrivilegeCategory.DoesNotExist, VIPLevel.DoesNotExist):
            return JsonResponse({
                'success': False,
                'message': '指定的分类或VIP等级不存在'
            }, status=400)

        # 检查特权名称是否重复
        if Privilege.objects.filter(name=data['name']).exists():
            return JsonResponse({
                'success': False,
                'message': '特权名称已存在'
            }, status=400)

        # 创建特权
        privilege = Privilege.objects.create(
            name=data['name'],
            category=category,
            description=data['description'],
            min_vip_level=vip_level,
            status=data.get('status', 'active'),
            sort_order=data.get('sort_order', 0),
            is_featured=data.get('is_featured', False)
        )

        # 创建统计记录
        PrivilegeStats.objects.create(privilege=privilege)

        return JsonResponse({
            'success': True,
            'message': '特权创建成功',
            'data': {
                'id': privilege.id,
                'name': privilege.name,
                'status': privilege.status
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': '无效的JSON数据'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'创建失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["PUT"])
def privilege_update_api(request, privilege_id):
    """更新特权API"""
    try:
        privilege = get_object_or_404(Privilege, id=privilege_id)
        data = json.loads(request.body)

        # 检查名称是否重复（排除自己）
        if data.get('name') and Privilege.objects.filter(name=data['name']).exclude(id=privilege_id).exists():
            return JsonResponse({
                'success': False,
                'message': '特权名称已存在'
            }, status=400)

        # 更新字段
        if data.get('name'):
            privilege.name = data['name']
        if data.get('description'):
            privilege.description = data['description']
        if data.get('status'):
            privilege.status = data['status']
        if data.get('category_id'):
            try:
                privilege.category = PrivilegeCategory.objects.get(id=data['category_id'])
            except PrivilegeCategory.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': '指定的分类不存在'
                }, status=400)
        if data.get('min_vip_level_id'):
            try:
                privilege.min_vip_level = VIPLevel.objects.get(id=data['min_vip_level_id'])
            except VIPLevel.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': '指定的VIP等级不存在'
                }, status=400)

        if 'sort_order' in data:
            privilege.sort_order = data['sort_order']
        if 'is_featured' in data:
            privilege.is_featured = data['is_featured']

        privilege.save()

        return JsonResponse({
            'success': True,
            'message': '特权更新成功',
            'data': {
                'id': privilege.id,
                'name': privilege.name,
                'status': privilege.status
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': '无效的JSON数据'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'更新失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["DELETE"])
def privilege_delete_api(request, privilege_id):
    """删除特权API"""
    try:
        privilege = get_object_or_404(Privilege, id=privilege_id)
        privilege_name = privilege.name

        # 删除特权（会级联删除相关的统计数据）
        privilege.delete()

        return JsonResponse({
            'success': True,
            'message': f'特权"{privilege_name}"已删除'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }, status=500)


def privilege_form_data_api(request):
    """获取表单数据API（分类和VIP等级）"""
    try:
        # 获取所有分类
        categories = []
        for category in PrivilegeCategory.objects.all():
            categories.append({
                'id': category.id,
                'name': category.name,
                'display_name': category.display_name,
                'icon': category.icon,
                'color': category.color
            })

        # 获取所有VIP等级
        vip_levels = []
        for vip_level in VIPLevel.objects.all():
            vip_levels.append({
                'id': vip_level.id,
                'level': vip_level.level,
                'name': vip_level.name,
                'threshold': str(vip_level.threshold)
            })

        return JsonResponse({
            'success': True,
            'data': {
                'categories': categories,
                'vip_levels': vip_levels,
                'status_choices': [
                    {'value': 'active', 'label': '已启用'},
                    {'value': 'disabled', 'label': '已禁用'},
                    {'value': 'maintenance', 'label': '维护中'}
                ]
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取表单数据失败: {str(e)}'
        }, status=500)



