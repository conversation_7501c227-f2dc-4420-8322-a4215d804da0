{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - MMO游戏大R用户维护系统{% endblock %}

{% block breadcrumb %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
/* 大R用户总览页面样式 */
.overview-container {
    padding: var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
}

/* 页面头部 */
.page-header {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.page-description {
    color: var(--muted-foreground);
    margin: 0;
    font-size: 1rem;
}

.last-update {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    font-size: 0.875rem;
    color: var(--muted-foreground);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.update-indicator {
    width: 8px;
    height: 8px;
    background: var(--success);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 核心指标卡片网格 */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.metric-card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary);
}

.metric-card.metric-success::before {
    background: var(--success);
}

.metric-card.metric-warning::before {
    background: var(--warning);
}

.metric-card.metric-error::before {
    background: var(--error);
}

.metric-card.metric-info::before {
    background: var(--info);
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.metric-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.metric-title {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--foreground);
    margin-bottom: var(--spacing-sm);
    line-height: 1;
}

.metric-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
}

.metric-change.positive {
    color: var(--success);
}

.metric-change.negative {
    color: var(--error);
}

.metric-change.neutral {
    color: var(--muted-foreground);
}

.change-icon {
    font-size: 0.75rem;
}

/* 图表和表格容器 */
.charts-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.chart-container {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.trend-type-selector {
    display: flex;
    gap: var(--spacing-xs);
    margin-right: var(--spacing-md);
}

.trend-btn {
    padding: 6px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--background);
    color: var(--muted-foreground);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 500;
}

.trend-btn.active {
    background: var(--success);
    color: white;
    border-color: var(--success);
}

.trend-btn:hover:not(.active) {
    background: var(--muted);
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground);
    margin: 0;
}

.time-selector {
    display: flex;
    gap: var(--spacing-xs);
}

.time-btn {
    padding: 6px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--background);
    color: var(--muted-foreground);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.time-btn.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.time-btn:hover:not(.active) {
    background: var(--muted);
}

.chart-content {
    height: 300px;
    position: relative;
}

/* VIP分布表格 */
.vip-distribution {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.vip-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
    background: var(--muted);
}

.vip-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground);
    margin: 0;
}

.vip-table {
    width: 100%;
}

.vip-row {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border);
    transition: var(--transition-fast);
}

.vip-row:hover {
    background: var(--muted);
}

.vip-row:last-child {
    border-bottom: none;
}

.vip-level {
    flex: 0 0 80px;
    font-weight: 600;
    color: var(--foreground);
}

.vip-badge {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    padding: 4px 8px;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 600;
}

.vip-count {
    flex: 1;
    text-align: center;
    font-weight: 500;
    color: var(--foreground);
}

.vip-progress {
    flex: 2;
    margin: 0 var(--spacing-md);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--muted);
    border-radius: var(--radius);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary), var(--info));
    border-radius: var(--radius);
    transition: width 0.5s ease;
}

.vip-percentage {
    flex: 0 0 60px;
    text-align: right;
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

/* 加载状态 */
.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--muted-foreground);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    }
}

@media (max-width: 768px) {
    .overview-container {
        padding: var(--spacing-md);
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .last-update {
        position: static;
        margin-top: var(--spacing-md);
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .chart-controls {
        width: 100%;
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .trend-type-selector {
        width: 100%;
        justify-content: center;
        margin-right: 0;
    }

    .time-selector {
        width: 100%;
        justify-content: center;
    }
    
    .vip-row {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    .vip-progress {
        width: 100%;
        margin: 0;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="overview-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">{{ page_title }}</h1>
        <p class="page-description">{{ page_description }}</p>
        <div class="last-update">
            <div class="update-indicator"></div>
            <span>实时更新 · 最后更新: <span id="lastUpdateTime">--:--</span></span>
        </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-header">
                <div class="metric-title">大R用户总数</div>
                <div class="metric-icon">
                    <i class="bi bi-people-fill"></i>
                </div>
            </div>
            <div class="metric-value" id="totalBigRUsers">--</div>
            <div class="metric-change neutral" id="totalUsersChange">
                <i class="bi bi-dash change-icon"></i>
                <span>统计中...</span>
            </div>
        </div>
        
        <div class="metric-card metric-success">
            <div class="metric-header">
                <div class="metric-title">平均ARPU值</div>
                <div class="metric-icon" style="background: var(--success);">
                    <i class="bi bi-currency-dollar"></i>
                </div>
            </div>
            <div class="metric-value" id="avgArpu">--</div>
            <div class="metric-change positive" id="arpuChange">
                <i class="bi bi-arrow-up change-icon"></i>
                <span>较上月 +--</span>
            </div>
        </div>
        
        <div class="metric-card metric-warning">
            <div class="metric-header">
                <div class="metric-title">流失预警用户</div>
                <div class="metric-icon" style="background: var(--warning);">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                </div>
            </div>
            <div class="metric-value" id="churnWarningUsers">--</div>
            <div class="metric-change negative" id="churnChange">
                <i class="bi bi-arrow-down change-icon"></i>
                <span>需要关注</span>
            </div>
        </div>
        
        <div class="metric-card metric-info">
            <div class="metric-header">
                <div class="metric-title">潜力用户数量</div>
                <div class="metric-icon" style="background: var(--info);">
                    <i class="bi bi-gem"></i>
                </div>
            </div>
            <div class="metric-value" id="potentialUsers">--</div>
            <div class="metric-change positive" id="potentialChange">
                <i class="bi bi-arrow-up change-icon"></i>
                <span>持续增长</span>
            </div>
        </div>
    </div>

    <!-- 图表和VIP分布 -->
    <div class="charts-grid">
        <!-- 用户增长趋势图 -->
        <div class="chart-container">
            <div class="chart-header">
                <h2 class="chart-title">用户增长趋势</h2>
                <div class="chart-controls">
                    <div class="trend-type-selector">
                        <button class="trend-btn active" data-type="cumulative">累计</button>
                        <button class="trend-btn" data-type="daily">当日</button>
                    </div>
                    <div class="time-selector">
                        <button class="time-btn" data-period="7">7天</button>
                        <button class="time-btn active" data-period="30">30天</button>
                        <button class="time-btn" data-period="90">3个月</button>
                    </div>
                </div>
            </div>
            <div class="chart-content">
                <canvas id="growthChart"></canvas>
            </div>
        </div>
        
        <!-- VIP等级分布 -->
        <div class="vip-distribution">
            <div class="vip-header">
                <h2 class="vip-title">VIP等级分布</h2>
            </div>
            <div class="vip-table" id="vipDistribution">
                <!-- 动态加载的VIP分布数据 -->
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <span>加载中...</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 大R用户总览页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 大R用户总览页面加载');

    let growthChart = null;
    let currentPeriod = 30;
    let currentTrendType = 'cumulative';

    // 初始化页面
    initializePage();

    function initializePage() {
        // 更新最后更新时间
        updateLastUpdateTime();

        // 加载核心指标数据
        loadMetricsData();

        // 初始化图表
        initializeChart();

        // 加载VIP分布数据
        loadVipDistribution();

        // 绑定时间选择器事件
        bindTimeSelector();

        // 绑定趋势类型选择器事件
        bindTrendTypeSelector();

        // 设置自动刷新（5分钟）
        setInterval(refreshData, 5 * 60 * 1000);

        console.log('✅ 页面初始化完成');
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        // 这里可以集成更复杂的通知系统
    }

    // 更新最后更新时间
    function updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
        document.getElementById('lastUpdateTime').textContent = timeString;
    }

    // 加载核心指标数据
    async function loadMetricsData() {
        try {
            const response = await fetch('/big-r-overview/api/metrics/');
            const result = await response.json();

            if (result.success) {
                const data = result.data;

                // 转换数据格式
                const displayData = {
                    totalBigRUsers: data.total_big_r_users.value,
                    totalUsersChange: data.total_big_r_users.change,
                    avgArpu: data.avg_arpu.formatted,
                    arpuChange: data.avg_arpu.change,
                    churnWarningUsers: data.churn_warning_users.value,
                    churnChange: data.churn_warning_users.change,
                    potentialUsers: data.potential_users.value,
                    potentialChange: data.potential_users.change
                };

                // 更新指标显示
                updateMetricsDisplay(displayData);

                console.log('✅ 核心指标数据加载成功');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('❌ 核心指标数据加载失败:', error);
            // 显示错误提示
            showNotification('核心指标数据加载失败，请稍后重试', 'error');
        }
    }

    // 更新指标显示
    function updateMetricsDisplay(data) {
        // 使用动画效果更新数值
        animateValue('totalBigRUsers', 0, data.totalBigRUsers, 1000);
        animateValue('potentialUsers', 0, data.potentialUsers, 1000);
        animateValue('churnWarningUsers', 0, data.churnWarningUsers, 1000);

        // 更新ARPU值
        document.getElementById('avgArpu').textContent = data.avgArpu;

        // 更新变化指标
        updateChangeIndicator('totalUsersChange', data.totalUsersChange, 'positive');
        updateChangeIndicator('arpuChange', data.arpuChange, 'positive');
        updateChangeIndicator('churnChange', data.churnChange, 'negative');
        updateChangeIndicator('potentialChange', data.potentialChange, 'positive');
    }

    // 数值动画
    function animateValue(elementId, start, end, duration) {
        const element = document.getElementById(elementId);
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                current = end;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current).toLocaleString();
        }, 16);
    }

    // 更新变化指标
    function updateChangeIndicator(elementId, value, type) {
        const element = document.getElementById(elementId);
        const isPositive = value.startsWith('+');
        const isNegative = value.startsWith('-');

        // 更新图标
        const icon = element.querySelector('.change-icon');
        if (isPositive) {
            icon.className = 'bi bi-arrow-up change-icon';
            element.className = 'metric-change positive';
        } else if (isNegative) {
            icon.className = 'bi bi-arrow-down change-icon';
            element.className = type === 'negative' ? 'metric-change positive' : 'metric-change negative';
        } else {
            icon.className = 'bi bi-dash change-icon';
            element.className = 'metric-change neutral';
        }

        // 更新文本
        const span = element.querySelector('span');
        if (elementId === 'totalUsersChange') {
            span.textContent = `较上月 ${value}`;
        } else if (elementId === 'arpuChange') {
            span.textContent = `较上月 ${value}`;
        } else if (elementId === 'churnChange') {
            span.textContent = `较上月 ${value}`;
        } else if (elementId === 'potentialChange') {
            span.textContent = `较上月 ${value}`;
        }
    }

    // 初始化图表
    function initializeChart() {
        const ctx = document.getElementById('growthChart').getContext('2d');

        growthChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '大R用户数量',
                    data: [],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280'
                        }
                    },
                    y: {
                        beginAtZero: false,
                        grid: {
                            color: 'rgba(107, 114, 128, 0.1)'
                        },
                        ticks: {
                            color: '#6b7280',
                            callback: function(value) {
                                return value.toLocaleString();
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#3b82f6'
                    }
                }
            }
        });

        // 加载图表数据
        loadChartData(currentPeriod, currentTrendType);
    }

    // 加载图表数据
    async function loadChartData(period, trendType = 'cumulative') {
        try {
            const response = await fetch(`/big-r-overview/api/growth-trend/?period=${period}&type=${trendType}`);
            const result = await response.json();

            if (result.success) {
                const data = result.data;

                // 更新图表标签和数据
                growthChart.data.labels = data.labels;
                growthChart.data.datasets[0].data = data.datasets[0].data;
                growthChart.data.datasets[0].label = data.datasets[0].label;

                // 根据趋势类型调整图表样式
                if (trendType === 'daily') {
                    // 当日新增使用柱状图样式
                    growthChart.config.type = 'bar';
                    growthChart.data.datasets[0].backgroundColor = 'rgba(34, 197, 94, 0.8)';
                    growthChart.data.datasets[0].borderColor = '#22c55e';
                    growthChart.data.datasets[0].fill = false;
                } else {
                    // 累计使用线图样式
                    growthChart.config.type = 'line';
                    growthChart.data.datasets[0].backgroundColor = 'rgba(59, 130, 246, 0.1)';
                    growthChart.data.datasets[0].borderColor = '#3b82f6';
                    growthChart.data.datasets[0].fill = true;
                }

                growthChart.update('active');

                console.log(`✅ ${period}天${trendType === 'daily' ? '当日' : '累计'}图表数据加载成功`);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('❌ 图表数据加载失败:', error);
            showNotification('图表数据加载失败，请稍后重试', 'error');
        }
    }



    // 加载VIP分布数据
    async function loadVipDistribution() {
        try {
            const response = await fetch('/big-r-overview/api/vip-distribution/');
            const result = await response.json();

            if (result.success) {
                // 渲染VIP分布
                renderVipDistribution(result.data);

                console.log('✅ VIP分布数据加载成功');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('❌ VIP分布数据加载失败:', error);
            showNotification('VIP分布数据加载失败，请稍后重试', 'error');
        }
    }

    // 渲染VIP分布
    function renderVipDistribution(data) {
        const container = document.getElementById('vipDistribution');

        container.innerHTML = '';

        data.forEach((item, index) => {
            const row = document.createElement('div');
            row.className = 'vip-row';

            row.innerHTML = `
                <div class="vip-level">
                    <span class="vip-badge">${item.level}</span>
                </div>
                <div class="vip-count">${item.count.toLocaleString()}</div>
                <div class="vip-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                </div>
                <div class="vip-percentage">${item.percentage}%</div>
            `;

            container.appendChild(row);

            // 动画效果
            setTimeout(() => {
                const progressFill = row.querySelector('.progress-fill');
                progressFill.style.width = `${item.percentage * 5}%`; // 放大显示效果
            }, index * 100);
        });
    }

    // 绑定时间选择器事件
    function bindTimeSelector() {
        const timeButtons = document.querySelectorAll('.time-btn');

        timeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // 更新按钮状态
                timeButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // 更新当前周期
                currentPeriod = parseInt(btn.dataset.period);

                // 重新加载图表数据
                loadChartData(currentPeriod, currentTrendType);
            });
        });
    }

    // 绑定趋势类型选择器事件
    function bindTrendTypeSelector() {
        const trendButtons = document.querySelectorAll('.trend-btn');

        trendButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // 更新按钮状态
                trendButtons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');

                // 更新当前趋势类型
                currentTrendType = btn.dataset.type;

                // 重新加载图表数据
                loadChartData(currentPeriod, currentTrendType);
            });
        });
    }

    // 刷新所有数据
    function refreshData() {
        console.log('🔄 自动刷新数据');
        updateLastUpdateTime();
        loadMetricsData();
        loadChartData(currentPeriod, currentTrendType);
        loadVipDistribution();
    }
});
</script>
{% endblock %}
