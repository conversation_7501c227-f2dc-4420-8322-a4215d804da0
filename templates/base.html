<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MMO游戏大R用户维护系统{% endblock %}</title>

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token }}">

    <!-- 基础样式 -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" href="{% static 'css/components.css' %}">
    <link rel="stylesheet" href="{% static 'css/layout.css' %}">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="app-layout">
        <!-- 侧边栏导航 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <h1 class="brand-title">大R维护系统</h1>
                    <p class="brand-subtitle">VIP用户管理平台</p>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle" aria-label="切换侧边栏">
                    <i class="bi bi-list"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="{% url 'big_r_overview:overview' %}" class="nav-link {% if request.resolver_match.namespace == 'big_r_overview' %}active{% endif %}">
                            <i class="bi bi-speedometer2"></i>
                            <span class="nav-text">大R用户总览</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'user_lifecycle:lifecycle' %}" class="nav-link {% if request.resolver_match.namespace == 'user_lifecycle' %}active{% endif %}">
                            <i class="bi bi-person-lines-fill"></i>
                            <span class="nav-text">生命周期管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="bi bi-exclamation-triangle"></i>
                            <span class="nav-text">流失预警中心</span>
                            <span class="nav-badge">12</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="bi bi-graph-up-arrow"></i>
                            <span class="nav-text">潜力挖掘分析</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'service_analysis:process_list' %}" class="nav-link {% if request.resolver_match.namespace == 'service_analysis' %}active{% endif %}">
                            <i class="bi bi-list-check"></i>
                            <span class="nav-text">特权处理列表</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'privileges:management' %}" class="nav-link {% if request.resolver_match.url_name == 'management' %}active{% endif %}">
                            <i class="bi bi-award"></i>
                            <span class="nav-text">特权管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link">
                            <i class="bi bi-gear"></i>
                            <span class="nav-text">系统配置</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="system-info">
                    <p class="version">v1.0.0</p>
                    <p class="status">
                        <span class="status-dot status-online"></span>
                        系统正常
                    </p>
                </div>
            </div>
        </aside>
        
        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-header">
                <div class="header-left">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="切换菜单">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="breadcrumb">
                        <span class="breadcrumb-item">{% block breadcrumb %}首页{% endblock %}</span>
                    </div>
                </div>
                
                <div class="header-right">
                    <button class="header-btn" id="notificationBtn" aria-label="通知">
                        <i class="bi bi-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <button class="header-btn" id="themeToggle" aria-label="切换主题">
                        <i class="bi bi-moon"></i>
                    </button>
                    
                    <div class="user-menu">
                        <button class="user-avatar" id="userMenuToggle" aria-label="用户菜单">
                            <img src="https://ui-avatars.com/api/?name=Admin&background=3b82f6&color=fff" alt="用户头像">
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#" class="dropdown-item">
                                <i class="bi bi-person"></i>
                                个人资料
                            </a>
                            <a href="#" class="dropdown-item">
                                <i class="bi bi-gear"></i>
                                设置
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item">
                                <i class="bi bi-box-arrow-right"></i>
                                退出登录
                            </a>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 页面内容 -->
            <div class="page-content">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>
    
    <!-- 基础JavaScript -->
    <script src="{% static 'js/base.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
