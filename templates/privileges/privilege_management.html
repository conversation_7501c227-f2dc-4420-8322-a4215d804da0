{% extends 'base.html' %}
{% load static %}

{% block title %}特权管理 - MMO游戏大R用户维护系统{% endblock %}

{% block breadcrumb %}特权管理{% endblock %}

{% block extra_css %}
<style>
/* 特权图标渐变色 */
.privilege-icon.gift {
    background: linear-gradient(135deg, #10b981, #059669);
}

.privilege-icon.percent {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.privilege-icon.speed {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.privilege-icon.star {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.privilege-icon.shield {
    background: linear-gradient(135deg, var(--primary), #4f46e5);
}

/* 搜索框动画 */
.search-input:focus {
    transform: scale(1.01);
}

/* 按钮光泽效果 */
.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* 加载状态样式 */
.loading-container {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--muted-foreground);
}

.loading-spinner i {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--muted-foreground);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--foreground);
}

.empty-state p {
    margin-bottom: var(--spacing-lg);
}

/* 紧凑模式额外优化 */
@media (max-width: 768px) {
    .privileges-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .stat-card {
        padding: var(--spacing-sm);
    }

    .stat-value {
        font-size: 1.25rem;
    }

    .stat-icon {
        width: 28px;
        height: 28px;
        font-size: 0.875rem;
    }

    .toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-sm);
    }

    .toolbar-search {
        min-width: auto;
    }

    .toolbar-actions {
        justify-content: center;
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(-20px);
    transition: all 0.3s ease;
}

.modal-overlay.show .modal-container {
    transform: scale(1) translateY(0);
}

.modal-sm {
    max-width: 400px;
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--foreground);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--muted-foreground);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius);
    transition: var(--transition-fast);
    font-size: 1.25rem;
}

.modal-close:hover {
    background-color: var(--muted);
    color: var(--foreground);
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-form {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* 表单样式 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--foreground);
    margin-bottom: var(--spacing-xs);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    background-color: var(--background);
    color: var(--foreground);
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px hsla(221.2, 83.2%, 53.3%, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-help {
    font-size: 0.75rem;
    color: var(--muted-foreground);
    margin-top: var(--spacing-xs);
}

/* 复选框样式 */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    margin-top: 24px;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border);
    border-radius: var(--radius-sm);
    position: relative;
    transition: var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background-color: var(--primary);
    border-color: var(--primary);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 删除警告样式 */
.delete-warning {
    text-align: center;
    padding: var(--spacing-lg);
}

.delete-warning i {
    font-size: 3rem;
    color: var(--warning);
    margin-bottom: var(--spacing-md);
}

.delete-warning p {
    margin-bottom: var(--spacing-sm);
    color: var(--foreground);
}

.warning-text {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

/* 按钮加载状态 */
.btn-loading .spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 响应式 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-container {
        width: 95%;
        margin: var(--spacing-md);
    }
}
</style>
{% endblock %}

{% block content %}
<!-- 页面头部 -->
<div class="page-header">
    <h1 class="page-title">{{ page_title }}</h1>
    <p class="page-description">{{ page_description }}</p>
</div>

<!-- 统计概览 -->
<div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">特权总数</div>
            <div class="stat-icon">
                <i class="bi bi-award"></i>
            </div>
        </div>
        <div class="stat-value" id="totalPrivileges">{{ stats.total_privileges }}</div>
        <div class="stat-change positive">
            <i class="bi bi-arrow-up"></i>
            <span>系统特权</span>
        </div>
    </div>

    <div class="stat-card stat-success">
        <div class="stat-header">
            <div class="stat-title">已启用特权</div>
            <div class="stat-icon" style="background: var(--success);">
                <i class="bi bi-check-circle"></i>
            </div>
        </div>
        <div class="stat-value" id="activePrivileges">{{ stats.active_privileges }}</div>
        <div class="stat-change positive">
            <i class="bi bi-arrow-up"></i>
            <span id="activeRate">{% if stats.total_privileges > 0 %}{{ stats.active_privileges|floatformat:0 }}/{{ stats.total_privileges }} 启用{% else %}0% 启用率{% endif %}</span>
        </div>
    </div>

    <div class="stat-card stat-error">
        <div class="stat-header">
            <div class="stat-title">已禁用特权</div>
            <div class="stat-icon" style="background: var(--error);">
                <i class="bi bi-x-circle"></i>
            </div>
        </div>
        <div class="stat-value" id="disabledPrivileges">{{ stats.disabled_privileges }}</div>
        <div class="stat-change negative">
            <i class="bi bi-arrow-down"></i>
            <span id="disabledRate">{% if stats.total_privileges > 0 %}{{ stats.disabled_privileges|floatformat:0 }}/{{ stats.total_privileges }} 禁用{% else %}0% 禁用率{% endif %}</span>
        </div>
    </div>

    <div class="stat-card stat-info">
        <div class="stat-header">
            <div class="stat-title">活跃使用用户</div>
            <div class="stat-icon" style="background: var(--info);">
                <i class="bi bi-people"></i>
            </div>
        </div>
        <div class="stat-value" id="totalActiveUsers">{{ stats.total_active_users|floatformat:0 }}</div>
        <div class="stat-change positive">
            <i class="bi bi-arrow-up"></i>
            <span>总活跃用户</span>
        </div>
    </div>
</div>

<!-- 工具栏 -->
<div class="toolbar">
    <div class="toolbar-search">
        <div class="input-group">
            <i class="bi bi-search input-icon"></i>
            <input type="text" class="input search-input" id="searchInput" placeholder="搜索特权名称、类型或描述...">
        </div>
    </div>
    
    <div class="toolbar-actions">
        <button class="btn btn-primary">
            <i class="bi bi-plus"></i>
            添加特权
        </button>
        <button class="btn btn-secondary">
            <i class="bi bi-upload"></i>
            批量导入
        </button>
        <button class="btn btn-outline">
            <i class="bi bi-download"></i>
            导出配置
        </button>
    </div>
</div>

<!-- 特权卡片网格 -->
<div class="privileges-grid" id="privilegesGrid">
    <!-- 加载中状态 -->
    <div class="loading-container" id="loadingContainer">
        <div class="loading-spinner">
            <i class="bi bi-arrow-clockwise"></i>
            <span>正在加载特权数据...</span>
        </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" id="emptyState" style="display: none;">
        <div class="empty-icon">
            <i class="bi bi-inbox"></i>
        </div>
        <h3>暂无特权数据</h3>
        <p>点击"添加特权"按钮创建第一个特权</p>
        <button class="btn btn-primary" onclick="showAddModal()">
            <i class="bi bi-plus"></i>
            添加特权
        </button>
    </div>

    <!-- 动态加载的特权卡片将在这里显示 -->
</div>

<!-- 添加/编辑特权模态框 -->
<div class="modal-overlay" id="privilegeModal" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h2 class="modal-title" id="modalTitle">添加特权</h2>
            <button class="modal-close" id="modalClose">
                <i class="bi bi-x"></i>
            </button>
        </div>

        <form class="modal-form" id="privilegeForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="privilegeName">特权名称 *</label>
                    <input type="text" id="privilegeName" name="name" required maxlength="100" placeholder="请输入特权名称">
                </div>

                <div class="form-group">
                    <label for="privilegeCategory">特权分类 *</label>
                    <select id="privilegeCategory" name="category_id" required>
                        <option value="">请选择分类</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="privilegeVipLevel">最低VIP等级 *</label>
                    <select id="privilegeVipLevel" name="min_vip_level_id" required>
                        <option value="">请选择VIP等级</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="privilegeStatus">状态</label>
                    <select id="privilegeStatus" name="status">
                        <option value="active">已启用</option>
                        <option value="disabled">已禁用</option>
                        <option value="maintenance">维护中</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label for="privilegeDescription">特权描述 *</label>
                <textarea id="privilegeDescription" name="description" required rows="4" placeholder="请输入特权描述"></textarea>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="privilegeSortOrder">排序</label>
                    <input type="number" id="privilegeSortOrder" name="sort_order" value="0" min="0">
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="privilegeFeatured" name="is_featured">
                        <span class="checkbox-custom"></span>
                        推荐特权
                    </label>
                </div>
            </div>


        </form>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="modalCancel">取消</button>
            <button type="submit" class="btn btn-primary" id="modalSubmit" form="privilegeForm">
                <span class="btn-text">保存</span>
                <span class="btn-loading" style="display: none;">
                    <i class="bi bi-arrow-clockwise spin"></i>
                    保存中...
                </span>
            </button>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal-overlay" id="deleteModal" style="display: none;">
    <div class="modal-container modal-sm">
        <div class="modal-header">
            <h2 class="modal-title">确认删除</h2>
            <button class="modal-close" id="deleteModalClose">
                <i class="bi bi-x"></i>
            </button>
        </div>

        <div class="modal-body">
            <div class="delete-warning">
                <i class="bi bi-exclamation-triangle"></i>
                <p>确定要删除特权 "<span id="deletePrivilegeName"></span>" 吗？</p>
                <p class="warning-text">此操作不可撤销，相关的统计数据也将被删除。</p>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="deleteCancel">取消</button>
            <button type="button" class="btn btn-error" id="deleteConfirm">
                <span class="btn-text">确认删除</span>
                <span class="btn-loading" style="display: none;">
                    <i class="bi bi-arrow-clockwise spin"></i>
                    删除中...
                </span>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/privilege-management.js' %}"></script>
{% endblock %}
