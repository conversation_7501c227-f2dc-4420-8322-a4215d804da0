{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - MMO游戏大R用户维护系统{% endblock %}

{% block breadcrumb %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
/* 特权处理列表页面样式 */
.process-container {
    padding: var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
}

/* 页面头部 */
.page-header {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.page-description {
    color: var(--muted-foreground);
    margin: 0;
}

/* 工具栏 */
.toolbar {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.toolbar-search {
    flex: 1;
    min-width: 300px;
}

.toolbar-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    background-color: var(--background);
    color: var(--foreground);
}

.toolbar-filters {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.filter-select {
    padding: 6px 10px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    background-color: var(--background);
    color: var(--foreground);
    min-width: 120px;
}

.toolbar-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* 处理记录列表 */
.records-container {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.records-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border);
    background: var(--muted);
}

.records-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground);
    margin: 0;
}

.records-list {
    max-height: 700px;
    overflow-y: auto;
}

.record-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border);
    transition: var(--transition-fast);
    cursor: pointer;
}

.record-item:hover {
    background: var(--muted);
}

.record-item:last-child {
    border-bottom: none;
}

.record-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.character-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.character-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--foreground);
}

.character-id {
    font-family: var(--font-mono);
    font-size: 0.875rem;
    color: var(--muted-foreground);
    background: var(--muted);
    padding: 2px 6px;
    border-radius: var(--radius);
}

.vip-badge {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    padding: 2px 8px;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 600;
}

.record-time {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.record-content {
    margin-bottom: var(--spacing-sm);
}

.privilege-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}

.privilege-icon {
    width: 24px;
    height: 24px;
    background: var(--primary);
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.privilege-name {
    font-weight: 500;
    color: var(--foreground);
}

.privilege-category {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.process-detail {
    color: var(--muted-foreground);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: var(--spacing-sm);
}

.record-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.processor-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* 加载状态 */
.loading-container {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--muted-foreground);
}

.loading-spinner {
    display: inline-block;
    width: 32px;
    height: 32px;
    border: 3px solid var(--border);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--muted-foreground);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* 分页 */
.pagination-container {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.pagination-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.pagination-btn {
    padding: 6px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--background);
    color: var(--foreground);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.pagination-btn:hover:not(:disabled) {
    background: var(--muted);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .process-container {
        padding: var(--spacing-md);
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar-search {
        min-width: auto;
    }
    
    .toolbar-filters {
        justify-content: center;
    }
    
    .record-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .character-info {
        flex-wrap: wrap;
    }
    
    .record-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(-20px);
    transition: all 0.3s ease;
}

.modal-overlay.show .modal-container {
    transform: scale(1) translateY(0);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--foreground);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--muted-foreground);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius);
    transition: var(--transition-fast);
    font-size: 1.25rem;
}

.modal-close:hover {
    background-color: var(--muted);
    color: var(--foreground);
}

.modal-form {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* 表单样式 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--foreground);
    margin-bottom: var(--spacing-xs);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    background-color: var(--background);
    color: var(--foreground);
    transition: var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px hsla(221.2, 83.2%, 53.3%, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* 按钮加载状态 */
.btn-loading .spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 响应式模态框 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .modal-container {
        width: 95%;
        margin: var(--spacing-md);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="process-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">{{ page_title }}</h1>
        <p class="page-description">{{ page_description }}</p>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
        <div class="toolbar-search">
            <input type="text" id="searchInput" placeholder="搜索角色名、角色ID、处理详情...">
        </div>
        
        <div class="toolbar-filters">
            <select id="privilegeFilter" class="filter-select">
                <option value="">所有特权类型</option>
            </select>
            
            <select id="vipLevelFilter" class="filter-select">
                <option value="">所有VIP等级</option>
            </select>
            
            <select id="processorFilter" class="filter-select">
                <option value="">所有处理人</option>
            </select>
            
            <input type="date" id="dateFromFilter" class="filter-select">
            <input type="date" id="dateToFilter" class="filter-select">
        </div>
        
        <div class="toolbar-actions">
            <button class="btn btn-primary">
                <i class="bi bi-plus"></i>
                添加记录
            </button>
            <button class="btn btn-secondary">
                <i class="bi bi-download"></i>
                导出数据
            </button>
        </div>
    </div>

    <!-- 处理记录列表 -->
    <div class="records-container">
        <div class="records-header">
            <h2 class="records-title">特权处理记录</h2>
        </div>
        
        <div class="records-list" id="recordsList">
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingContainer">
                <div class="loading-spinner"></div>
                <div>正在加载处理记录...</div>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">
                    <i class="bi bi-inbox"></i>
                </div>
                <h3>暂无处理记录</h3>
                <p>没有找到符合条件的特权处理记录</p>
            </div>
            
            <!-- 动态加载的处理记录将在这里显示 -->
        </div>
        
        <!-- 分页 -->
        <div class="pagination-container" id="paginationContainer" style="display: none;">
            <div class="pagination-info" id="paginationInfo">
                显示第 1-20 条，共 100 条记录
            </div>
            <div class="pagination-controls">
                <button class="pagination-btn" id="prevPageBtn">上一页</button>
                <button class="pagination-btn" id="nextPageBtn">下一页</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加记录模态框 -->
<div class="modal-overlay" id="addRecordModal" style="display: none;">
    <div class="modal-container">
        <div class="modal-header">
            <h2 class="modal-title">添加特权处理记录</h2>
            <button class="modal-close" id="modalClose">
                <i class="bi bi-x"></i>
            </button>
        </div>

        <form class="modal-form" id="addRecordForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="characterName">角色名 *</label>
                    <input type="text" id="characterName" name="character_name" required maxlength="100" placeholder="请输入角色名">
                </div>

                <div class="form-group">
                    <label for="characterId">角色ID *</label>
                    <input type="number" id="characterId" name="character_id" required min="1" placeholder="请输入角色ID">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="vipLevel">VIP等级 *</label>
                    <select id="vipLevel" name="vip_level" required>
                        <option value="">请选择VIP等级</option>
                        <option value="0">普通用户</option>
                        <option value="1">VIP1</option>
                        <option value="2">VIP2</option>
                        <option value="3">VIP3</option>
                        <option value="4">VIP4</option>
                        <option value="5">VIP5</option>
                        <option value="6">VIP6</option>
                        <option value="7">VIP7</option>
                        <option value="8">VIP8</option>
                        <option value="9">VIP9</option>
                        <option value="10">VIP10</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="privilegeType">特权类型 *</label>
                    <select id="privilegeType" name="privilege_id" required>
                        <option value="">请选择特权类型</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label for="processDetail">处理详情 *</label>
                <textarea id="processDetail" name="process_detail" required rows="4" placeholder="请详细描述特权处理的具体内容..."></textarea>
            </div>

            <div class="form-group">
                <label for="processor">处理人 *</label>
                <input type="text" id="processor" name="processor" required maxlength="100" placeholder="请输入处理人姓名">
            </div>
        </form>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="modalCancel">取消</button>
            <button type="submit" class="btn btn-primary" id="modalSubmit" form="addRecordForm">
                <span class="btn-text">添加记录</span>
                <span class="btn-loading" style="display: none;">
                    <i class="bi bi-arrow-clockwise spin"></i>
                    添加中...
                </span>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 特权处理列表页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 特权处理列表页面加载');
    
    let currentPage = 1;
    let totalPages = 1;
    
    // 初始化页面
    initializePage();

    function initializePage() {
        // 加载筛选选项
        loadFilterOptions();

        // 加载处理记录
        loadRecords();

        // 绑定搜索事件
        bindSearchEvents();

        // 绑定筛选事件
        bindFilterEvents();

        // 绑定分页事件
        bindPaginationEvents();

        // 初始化模态框
        initializeModal();

        console.log('✅ 页面初始化完成');
    }

    // 初始化模态框
    function initializeModal() {
        const addRecordModal = document.getElementById('addRecordModal');
        const modalClose = document.getElementById('modalClose');
        const modalCancel = document.getElementById('modalCancel');
        const addRecordForm = document.getElementById('addRecordForm');
        const addRecordBtn = document.querySelector('.toolbar-actions .btn-primary');

        // 绑定添加记录按钮事件
        if (addRecordBtn) {
            addRecordBtn.addEventListener('click', showAddRecordModal);
        }

        // 关闭模态框事件
        [modalClose, modalCancel].forEach(btn => {
            if (btn) {
                btn.addEventListener('click', hideAddRecordModal);
            }
        });

        // 点击遮罩关闭模态框
        if (addRecordModal) {
            addRecordModal.addEventListener('click', (e) => {
                if (e.target === addRecordModal) {
                    hideAddRecordModal();
                }
            });
        }

        // 表单提交
        if (addRecordForm) {
            addRecordForm.addEventListener('submit', handleFormSubmit);
        }

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && addRecordModal.classList.contains('show')) {
                hideAddRecordModal();
            }
        });
    }
    
    // 加载筛选选项
    async function loadFilterOptions() {
        try {
            const response = await fetch('/service-analysis/api/filter-options/');
            const data = await response.json();
            
            if (data.success) {
                populateFilterOptions(data.data);
                console.log('✅ 筛选选项加载成功');
            } else {
                console.error('❌ 筛选选项加载失败:', data.message);
            }
        } catch (error) {
            console.error('❌ 筛选选项加载失败:', error);
        }
    }
    
    // 填充筛选选项
    function populateFilterOptions(options) {
        // 填充特权类型选项
        const privilegeFilter = document.getElementById('privilegeFilter');
        if (privilegeFilter && options.privileges) {
            privilegeFilter.innerHTML = '<option value="">所有特权类型</option>';
            options.privileges.forEach(privilege => {
                const option = document.createElement('option');
                option.value = privilege.id;
                option.textContent = `${privilege.name} (${privilege.category})`;
                privilegeFilter.appendChild(option);
            });
        }

        // 填充VIP等级选项
        const vipLevelFilter = document.getElementById('vipLevelFilter');
        if (vipLevelFilter && options.vip_levels) {
            vipLevelFilter.innerHTML = '<option value="">所有VIP等级</option>';
            options.vip_levels.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level === 0 ? '普通用户' : `VIP${level}`;
                vipLevelFilter.appendChild(option);
            });
        }

        // 填充处理人选项
        const processorFilter = document.getElementById('processorFilter');
        if (processorFilter && options.processors) {
            processorFilter.innerHTML = '<option value="">所有处理人</option>';
            options.processors.forEach(processor => {
                const option = document.createElement('option');
                option.value = processor;
                option.textContent = processor;
                processorFilter.appendChild(option);
            });
        }

        // 填充模态框中的特权类型选项
        const privilegeTypeSelect = document.getElementById('privilegeType');
        if (privilegeTypeSelect && options.privileges) {
            privilegeTypeSelect.innerHTML = '<option value="">请选择特权类型</option>';
            options.privileges.forEach(privilege => {
                const option = document.createElement('option');
                option.value = privilege.id;
                option.textContent = `${privilege.name} (${privilege.category})`;
                privilegeTypeSelect.appendChild(option);
            });
        }
    }

    // 显示添加记录模态框
    function showAddRecordModal() {
        console.log('🆕 显示添加记录模态框');

        // 重置表单
        resetForm();

        // 显示模态框
        showModal('addRecordModal');
    }

    // 隐藏添加记录模态框
    function hideAddRecordModal() {
        hideModal('addRecordModal');
    }

    // 显示模态框
    function showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'flex';
            setTimeout(() => modal.classList.add('show'), 10);
            document.body.style.overflow = 'hidden';
        }
    }

    // 隐藏模态框
    function hideModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
                document.body.style.overflow = '';
            }, 300);
        }
    }

    // 重置表单
    function resetForm() {
        const form = document.getElementById('addRecordForm');
        if (form) {
            form.reset();
        }
    }

    // 表单提交处理
    async function handleFormSubmit(e) {
        e.preventDefault();

        const submitBtn = document.getElementById('modalSubmit');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        // 显示加载状态
        btnText.style.display = 'none';
        btnLoading.style.display = 'inline-flex';
        submitBtn.disabled = true;

        try {
            // 收集表单数据
            const formData = new FormData(e.target);
            const data = {};

            for (let [key, value] of formData.entries()) {
                data[key] = value.trim();
            }

            console.log('📤 提交数据:', data);

            // 发送请求
            const response = await fetch('/service-analysis/api/create/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                showNotification(result.message, 'success');
                hideAddRecordModal();

                // 重新加载数据
                loadRecords(1);
            } else {
                showNotification(result.message, 'error');
            }

        } catch (error) {
            console.error('❌ 表单提交失败:', error);
            showNotification('添加失败，请稍后重试', 'error');
        } finally {
            // 恢复按钮状态
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
            submitBtn.disabled = false;
        }
    }

    // 获取CSRF Token
    function getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return '';
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="bi bi-${getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--surface);
            border: 1px solid var(--border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-lg);
            z-index: 1100;
            min-width: 300px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;

        // 根据类型设置颜色
        if (type === 'success') {
            notification.style.borderLeftColor = 'var(--success)';
        } else if (type === 'error') {
            notification.style.borderLeftColor = 'var(--error)';
        } else if (type === 'warning') {
            notification.style.borderLeftColor = 'var(--warning)';
        } else {
            notification.style.borderLeftColor = 'var(--info)';
        }

        // 添加到页面
        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 10);

        // 自动移除
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 获取通知图标
    function getNotificationIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'x-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    // 加载处理记录
    async function loadRecords(page = 1) {
        try {
            showLoading();
            
            const params = new URLSearchParams({
                search: document.getElementById('searchInput')?.value || '',
                privilege_type: document.getElementById('privilegeFilter')?.value || '',
                vip_level: document.getElementById('vipLevelFilter')?.value || '',
                processor: document.getElementById('processorFilter')?.value || '',
                date_from: document.getElementById('dateFromFilter')?.value || '',
                date_to: document.getElementById('dateToFilter')?.value || '',
                page: page,
                page_size: 20
            });
            
            const response = await fetch(`/service-analysis/api/records/?${params}`);
            const data = await response.json();
            
            if (data.success) {
                hideLoading();
                
                if (data.data.records.length === 0) {
                    showEmptyState();
                    hidePagination();
                } else {
                    hideEmptyState();
                    renderRecords(data.data.records);
                    updatePagination(data.data.pagination);
                }
                
                console.log('✅ 处理记录加载成功:', data.data.records.length, '条记录');
            } else {
                hideLoading();
                showEmptyState();
                hidePagination();
                console.error('❌ 处理记录加载失败:', data.message);
            }
        } catch (error) {
            hideLoading();
            showEmptyState();
            hidePagination();
            console.error('❌ 处理记录加载失败:', error);
        }
    }
    
    // 渲染处理记录
    function renderRecords(records) {
        const recordsList = document.getElementById('recordsList');
        
        // 清空现有内容（保留加载和空状态元素）
        const existingRecords = recordsList.querySelectorAll('.record-item');
        existingRecords.forEach(record => record.remove());
        
        // 添加新的记录项
        records.forEach(record => {
            const recordElement = createRecordElement(record);
            recordsList.appendChild(recordElement);
        });
        
        console.log(`✅ 渲染了 ${records.length} 条处理记录`);
    }
    
    // 创建记录元素
    function createRecordElement(record) {
        const recordDiv = document.createElement('div');
        recordDiv.className = 'record-item';
        recordDiv.setAttribute('data-record-id', record.id);
        
        const timeAgo = getTimeAgo(record.processed_at);
        const vipDisplay = record.vip_level === 0 ? '普通用户' : `VIP${record.vip_level}`;
        
        recordDiv.innerHTML = `
            <div class="record-header">
                <div class="character-info">
                    <div class="character-name">${record.character_name}</div>
                    <div class="character-id">${record.character_id}</div>
                    <div class="vip-badge">${vipDisplay}</div>
                </div>
                <div class="record-time">${timeAgo}</div>
            </div>
            <div class="record-content">
                <div class="privilege-info">
                    <div class="privilege-icon">
                        <i class="${record.privilege.icon}"></i>
                    </div>
                    <div class="privilege-name">${record.privilege.name}</div>
                    <div class="privilege-category">${record.privilege.category}</div>
                </div>
                <div class="process-detail">${record.process_detail}</div>
            </div>
            <div class="record-footer">
                <div class="processor-info">
                    <i class="bi bi-person-check"></i>
                    <span>处理人: ${record.processor}</span>
                </div>
                <div class="process-time">
                    <i class="bi bi-clock"></i>
                    <span>${new Date(record.processed_at).toLocaleString()}</span>
                </div>
            </div>
        `;
        
        // 添加点击事件
        recordDiv.addEventListener('click', () => {
            console.log('点击处理记录:', record.id);
            // TODO: 打开记录详情
        });
        
        return recordDiv;
    }
    
    // 获取相对时间
    function getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);
        
        if (diffMins < 60) {
            return `${diffMins}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return date.toLocaleDateString();
        }
    }
    
    // 更新分页
    function updatePagination(pagination) {
        currentPage = pagination.current_page;
        totalPages = pagination.total_pages;
        
        const paginationContainer = document.getElementById('paginationContainer');
        const paginationInfo = document.getElementById('paginationInfo');
        const prevPageBtn = document.getElementById('prevPageBtn');
        const nextPageBtn = document.getElementById('nextPageBtn');
        
        if (pagination.total_count > 0) {
            paginationContainer.style.display = 'flex';
            
            const startIndex = (currentPage - 1) * 20 + 1;
            const endIndex = Math.min(currentPage * 20, pagination.total_count);
            paginationInfo.textContent = `显示第 ${startIndex}-${endIndex} 条，共 ${pagination.total_count} 条记录`;
            
            prevPageBtn.disabled = !pagination.has_previous;
            nextPageBtn.disabled = !pagination.has_next;
        } else {
            paginationContainer.style.display = 'none';
        }
    }
    
    // 显示加载状态
    function showLoading() {
        const loadingContainer = document.getElementById('loadingContainer');
        const emptyState = document.getElementById('emptyState');
        
        if (loadingContainer) loadingContainer.style.display = 'block';
        if (emptyState) emptyState.style.display = 'none';
        
        // 清空现有记录
        const existingRecords = document.querySelectorAll('.record-item');
        existingRecords.forEach(record => record.remove());
    }
    
    // 隐藏加载状态
    function hideLoading() {
        const loadingContainer = document.getElementById('loadingContainer');
        if (loadingContainer) loadingContainer.style.display = 'none';
    }
    
    // 显示空状态
    function showEmptyState() {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) emptyState.style.display = 'block';
    }
    
    // 隐藏空状态
    function hideEmptyState() {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) emptyState.style.display = 'none';
    }
    
    // 隐藏分页
    function hidePagination() {
        const paginationContainer = document.getElementById('paginationContainer');
        if (paginationContainer) paginationContainer.style.display = 'none';
    }
    
    // 绑定搜索事件
    function bindSearchEvents() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(handleSearch, 300));
        }
    }
    
    // 绑定筛选事件
    function bindFilterEvents() {
        const filters = ['privilegeFilter', 'vipLevelFilter', 'processorFilter', 'dateFromFilter', 'dateToFilter'];
        filters.forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', handleFilter);
            }
        });
    }
    
    // 绑定分页事件
    function bindPaginationEvents() {
        const prevPageBtn = document.getElementById('prevPageBtn');
        const nextPageBtn = document.getElementById('nextPageBtn');
        
        if (prevPageBtn) {
            prevPageBtn.addEventListener('click', () => {
                if (currentPage > 1) {
                    loadRecords(currentPage - 1);
                }
            });
        }
        
        if (nextPageBtn) {
            nextPageBtn.addEventListener('click', () => {
                if (currentPage < totalPages) {
                    loadRecords(currentPage + 1);
                }
            });
        }
    }
    
    // 搜索处理
    function handleSearch() {
        currentPage = 1;
        loadRecords(1);
    }
    
    // 筛选处理
    function handleFilter() {
        currentPage = 1;
        loadRecords(1);
    }
    
    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});
</script>
{% endblock %}
