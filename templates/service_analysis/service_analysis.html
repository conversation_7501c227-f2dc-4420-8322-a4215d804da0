{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - MMO游戏大R用户维护系统{% endblock %}

{% block breadcrumb %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
/* 特权处理分析页面样式 */
.analysis-container {
    padding: var(--spacing-lg);
    max-width: 1400px;
    margin: 0 auto;
}

/* 页面头部 */
.page-header {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.page-description {
    color: var(--muted-foreground);
    margin: 0;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary);
}

.stat-card.stat-success::before {
    background: var(--success);
}

.stat-card.stat-warning::before {
    background: var(--warning);
}

.stat-card.stat-error::before {
    background: var(--error);
}

.stat-card.stat-info::before {
    background: var(--info);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.stat-title {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    font-weight: 500;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius);
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--foreground);
    margin-bottom: var(--spacing-xs);
}

.stat-change {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--error);
}

.stat-change.neutral {
    color: var(--muted-foreground);
}

/* 工具栏 */
.toolbar {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.toolbar-search {
    flex: 1;
    min-width: 300px;
}

.toolbar-search input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    background-color: var(--background);
    color: var(--foreground);
}

.toolbar-filters {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.filter-select {
    padding: 6px 10px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    font-size: 0.875rem;
    background-color: var(--background);
    color: var(--foreground);
    min-width: 120px;
}

.toolbar-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* 工单列表 */
.tickets-container {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.tickets-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border);
    background: var(--muted);
}

.tickets-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground);
    margin: 0;
}

.tickets-list {
    max-height: 600px;
    overflow-y: auto;
}

.ticket-item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border);
    transition: var(--transition-fast);
    cursor: pointer;
}

.ticket-item:hover {
    background: var(--muted);
}

.ticket-item:last-child {
    border-bottom: none;
}

.ticket-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.ticket-id {
    font-family: var(--font-mono);
    font-size: 0.875rem;
    color: var(--primary);
    font-weight: 600;
}

.ticket-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.75rem;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: var(--radius);
}

.status-pending {
    background: hsl(38, 92%, 95%);
    color: hsl(38, 92%, 50%);
}

.status-processing {
    background: hsl(221, 83%, 95%);
    color: hsl(221, 83%, 53%);
}

.status-resolved {
    background: hsl(142, 76%, 95%);
    color: hsl(142, 76%, 36%);
}

.status-closed {
    background: hsl(215, 28%, 95%);
    color: hsl(215, 28%, 46%);
}

.ticket-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: var(--spacing-xs);
}

.ticket-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.ticket-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.vip-badge {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    padding: 1px 6px;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 600;
}

.ticket-time {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* 加载状态 */
.loading-container {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--muted-foreground);
}

.loading-spinner {
    display: inline-block;
    width: 32px;
    height: 32px;
    border: 3px solid var(--border);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--muted-foreground);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .analysis-container {
        padding: var(--spacing-md);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar-search {
        min-width: auto;
    }
    
    .toolbar-filters {
        justify-content: center;
    }
    
    .ticket-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
    
    .ticket-meta {
        flex-wrap: wrap;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="analysis-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">{{ page_title }}</h1>
        <p class="page-description">{{ page_description }}</p>
    </div>

    <!-- 统计概览 -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-title">工单总数</div>
                <div class="stat-icon">
                    <i class="bi bi-ticket-perforated"></i>
                </div>
            </div>
            <div class="stat-value" id="totalTickets">{{ stats.total_tickets }}</div>
            <div class="stat-change neutral">
                <i class="bi bi-info-circle"></i>
                <span>系统工单</span>
            </div>
        </div>
        
        <div class="stat-card stat-warning">
            <div class="stat-header">
                <div class="stat-title">待处理工单</div>
                <div class="stat-icon" style="background: var(--warning);">
                    <i class="bi bi-clock"></i>
                </div>
            </div>
            <div class="stat-value" id="pendingTickets">{{ stats.pending_tickets }}</div>
            <div class="stat-change neutral">
                <i class="bi bi-arrow-right"></i>
                <span>需要处理</span>
            </div>
        </div>
        
        <div class="stat-card stat-info">
            <div class="stat-header">
                <div class="stat-title">处理中工单</div>
                <div class="stat-icon" style="background: var(--info);">
                    <i class="bi bi-gear"></i>
                </div>
            </div>
            <div class="stat-value" id="processingTickets">{{ stats.processing_tickets }}</div>
            <div class="stat-change neutral">
                <i class="bi bi-arrow-right"></i>
                <span>正在处理</span>
            </div>
        </div>
        
        <div class="stat-card stat-success">
            <div class="stat-header">
                <div class="stat-title">今日已解决</div>
                <div class="stat-icon" style="background: var(--success);">
                    <i class="bi bi-check-circle"></i>
                </div>
            </div>
            <div class="stat-value" id="resolvedToday">{{ stats.resolved_today }}</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i>
                <span>今日完成</span>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-title">平均响应时间</div>
                <div class="stat-icon">
                    <i class="bi bi-stopwatch"></i>
                </div>
            </div>
            <div class="stat-value" id="avgResponseTime">{{ stats.avg_response_time }}分</div>
            <div class="stat-change neutral">
                <i class="bi bi-info-circle"></i>
                <span>响应速度</span>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-title">平均解决时间</div>
                <div class="stat-icon">
                    <i class="bi bi-hourglass-split"></i>
                </div>
            </div>
            <div class="stat-value" id="avgResolutionTime">{{ stats.avg_resolution_time }}小时</div>
            <div class="stat-change neutral">
                <i class="bi bi-info-circle"></i>
                <span>处理效率</span>
            </div>
        </div>
        
        <div class="stat-card stat-success">
            <div class="stat-header">
                <div class="stat-title">平均满意度</div>
                <div class="stat-icon" style="background: var(--success);">
                    <i class="bi bi-star"></i>
                </div>
            </div>
            <div class="stat-value" id="avgSatisfaction">{{ stats.avg_satisfaction }}</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i>
                <span>用户评价</span>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-title">处理效率</div>
                <div class="stat-icon">
                    <i class="bi bi-speedometer2"></i>
                </div>
            </div>
            <div class="stat-value" id="efficiency">85%</div>
            <div class="stat-change positive">
                <i class="bi bi-arrow-up"></i>
                <span>综合评分</span>
            </div>
        </div>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
        <div class="toolbar-search">
            <input type="text" id="searchInput" placeholder="搜索工单号、标题、用户名...">
        </div>
        
        <div class="toolbar-filters">
            <select id="categoryFilter" class="filter-select">
                <option value="">所有分类</option>
            </select>
            
            <select id="statusFilter" class="filter-select">
                <option value="">所有状态</option>
                <option value="pending">待处理</option>
                <option value="processing">处理中</option>
                <option value="waiting_user">等待用户</option>
                <option value="resolved">已解决</option>
                <option value="closed">已关闭</option>
            </select>
            
            <select id="priorityFilter" class="filter-select">
                <option value="">所有优先级</option>
                <option value="urgent">紧急</option>
                <option value="high">高</option>
                <option value="normal">普通</option>
                <option value="low">低</option>
            </select>
        </div>
        
        <div class="toolbar-actions">
            <button class="btn btn-primary">
                <i class="bi bi-plus"></i>
                新建工单
            </button>
            <button class="btn btn-secondary">
                <i class="bi bi-download"></i>
                导出报表
            </button>
        </div>
    </div>

    <!-- 工单列表 -->
    <div class="tickets-container">
        <div class="tickets-header">
            <h2 class="tickets-title">工单列表</h2>
        </div>
        
        <div class="tickets-list" id="ticketsList">
            <!-- 加载状态 -->
            <div class="loading-container" id="loadingContainer">
                <div class="loading-spinner"></div>
                <div>正在加载工单数据...</div>
            </div>
            
            <!-- 空状态 -->
            <div class="empty-state" id="emptyState" style="display: none;">
                <div class="empty-icon">
                    <i class="bi bi-inbox"></i>
                </div>
                <h3>暂无工单数据</h3>
                <p>没有找到符合条件的工单</p>
            </div>
            
            <!-- 动态加载的工单列表将在这里显示 -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 特权处理分析页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 特权处理分析页面加载');

    // 初始化页面
    initializePage();

    function initializePage() {
        // 加载统计数据
        loadStats();

        // 加载工单数据
        loadTickets();

        // 绑定搜索事件
        bindSearchEvents();

        // 绑定筛选事件
        bindFilterEvents();

        console.log('✅ 页面初始化完成');
    }

    // 加载统计数据
    async function loadStats() {
        try {
            const response = await fetch('/service-analysis/api/stats/');
            const data = await response.json();

            if (data.success) {
                updateStatsDisplay(data.data.overview);
                console.log('✅ 统计数据加载成功');
            } else {
                console.error('❌ 统计数据加载失败:', data.message);
            }
        } catch (error) {
            console.error('❌ 统计数据加载失败:', error);
        }
    }

    // 更新统计显示
    function updateStatsDisplay(stats) {
        const elements = {
            totalTickets: document.getElementById('totalTickets'),
            pendingTickets: document.getElementById('pendingTickets'),
            processingTickets: document.getElementById('processingTickets'),
            resolvedToday: document.getElementById('resolvedToday'),
            avgResponseTime: document.getElementById('avgResponseTime'),
            avgResolutionTime: document.getElementById('avgResolutionTime'),
            avgSatisfaction: document.getElementById('avgSatisfaction'),
        };

        if (elements.totalTickets) elements.totalTickets.textContent = stats.total_tickets;
        if (elements.pendingTickets) elements.pendingTickets.textContent = stats.pending_tickets;
        if (elements.processingTickets) elements.processingTickets.textContent = stats.processing_tickets;
        if (elements.resolvedToday) elements.resolvedToday.textContent = stats.today_resolved;
        if (elements.avgResponseTime) elements.avgResponseTime.textContent = stats.avg_response_time + '分';
        if (elements.avgResolutionTime) elements.avgResolutionTime.textContent = stats.avg_resolution_time + '小时';
        if (elements.avgSatisfaction) elements.avgSatisfaction.textContent = stats.avg_satisfaction;
    }

    // 加载工单数据
    async function loadTickets(search = '', category = '', status = '', priority = '') {
        try {
            showLoading();

            const params = new URLSearchParams({
                search: search,
                category: category,
                status: status,
                priority: priority,
                page: 1,
                page_size: 50
            });

            const response = await fetch(`/service-analysis/api/tickets/?${params}`);
            const data = await response.json();

            if (data.success) {
                hideLoading();

                if (data.data.tickets.length === 0) {
                    showEmptyState();
                } else {
                    hideEmptyState();
                    renderTickets(data.data.tickets);
                }

                console.log('✅ 工单数据加载成功:', data.data.tickets.length, '个工单');
            } else {
                hideLoading();
                showEmptyState();
                console.error('❌ 工单数据加载失败:', data.message);
            }
        } catch (error) {
            hideLoading();
            showEmptyState();
            console.error('❌ 工单数据加载失败:', error);
        }
    }

    // 渲染工单列表
    function renderTickets(tickets) {
        const ticketsList = document.getElementById('ticketsList');

        // 清空现有内容（保留加载和空状态元素）
        const existingTickets = ticketsList.querySelectorAll('.ticket-item');
        existingTickets.forEach(ticket => ticket.remove());

        // 添加新的工单项
        tickets.forEach(ticket => {
            const ticketElement = createTicketElement(ticket);
            ticketsList.appendChild(ticketElement);
        });

        console.log(`✅ 渲染了 ${tickets.length} 个工单`);
    }

    // 创建工单元素
    function createTicketElement(ticket) {
        const ticketDiv = document.createElement('div');
        ticketDiv.className = 'ticket-item';
        ticketDiv.setAttribute('data-ticket-id', ticket.id);

        const statusClass = `status-${ticket.status}`;
        const priorityIcon = getPriorityIcon(ticket.priority);
        const timeAgo = getTimeAgo(ticket.created_at);

        ticketDiv.innerHTML = `
            <div class="ticket-header">
                <div class="ticket-id">#${ticket.ticket_id}</div>
                <div class="ticket-status ${statusClass}">
                    <div class="status-dot"></div>
                    <span>${ticket.status_display}</span>
                </div>
            </div>
            <div class="ticket-title">${ticket.title}</div>
            <div class="ticket-meta">
                <div class="ticket-user">
                    <i class="bi bi-person"></i>
                    <span>${ticket.user_name}</span>
                    ${ticket.user_vip_level > 0 ? `<span class="vip-badge">VIP${ticket.user_vip_level}</span>` : ''}
                </div>
                <div class="ticket-category">
                    <i class="${ticket.category.icon}"></i>
                    <span>${ticket.category.display_name}</span>
                </div>
                <div class="ticket-priority">
                    <i class="${priorityIcon}"></i>
                    <span>${ticket.priority_display}</span>
                </div>
                <div class="ticket-time">
                    <i class="bi bi-clock"></i>
                    <span>${timeAgo}</span>
                </div>
                ${ticket.assigned_to ? `
                <div class="ticket-assigned">
                    <i class="bi bi-person-check"></i>
                    <span>${ticket.assigned_to}</span>
                </div>
                ` : ''}
            </div>
        `;

        // 添加点击事件
        ticketDiv.addEventListener('click', () => {
            console.log('点击工单:', ticket.ticket_id);
            // TODO: 打开工单详情
        });

        return ticketDiv;
    }

    // 获取优先级图标
    function getPriorityIcon(priority) {
        const icons = {
            'urgent': 'bi-exclamation-triangle-fill',
            'high': 'bi-arrow-up-circle',
            'normal': 'bi-dash-circle',
            'low': 'bi-arrow-down-circle'
        };
        return icons[priority] || 'bi-dash-circle';
    }

    // 获取相对时间
    function getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 60) {
            return `${diffMins}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return date.toLocaleDateString();
        }
    }

    // 显示加载状态
    function showLoading() {
        const loadingContainer = document.getElementById('loadingContainer');
        const emptyState = document.getElementById('emptyState');

        if (loadingContainer) loadingContainer.style.display = 'block';
        if (emptyState) emptyState.style.display = 'none';

        // 清空现有工单
        const existingTickets = document.querySelectorAll('.ticket-item');
        existingTickets.forEach(ticket => ticket.remove());
    }

    // 隐藏加载状态
    function hideLoading() {
        const loadingContainer = document.getElementById('loadingContainer');
        if (loadingContainer) loadingContainer.style.display = 'none';
    }

    // 显示空状态
    function showEmptyState() {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) emptyState.style.display = 'block';
    }

    // 隐藏空状态
    function hideEmptyState() {
        const emptyState = document.getElementById('emptyState');
        if (emptyState) emptyState.style.display = 'none';
    }

    // 绑定搜索事件
    function bindSearchEvents() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', debounce(handleSearch, 300));
        }
    }

    // 绑定筛选事件
    function bindFilterEvents() {
        const filters = ['categoryFilter', 'statusFilter', 'priorityFilter'];
        filters.forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', handleFilter);
            }
        });
    }

    // 搜索处理
    function handleSearch() {
        const searchInput = document.getElementById('searchInput');
        const search = searchInput ? searchInput.value.trim() : '';

        const category = document.getElementById('categoryFilter')?.value || '';
        const status = document.getElementById('statusFilter')?.value || '';
        const priority = document.getElementById('priorityFilter')?.value || '';

        loadTickets(search, category, status, priority);
    }

    // 筛选处理
    function handleFilter() {
        const searchInput = document.getElementById('searchInput');
        const search = searchInput ? searchInput.value.trim() : '';

        const category = document.getElementById('categoryFilter')?.value || '';
        const status = document.getElementById('statusFilter')?.value || '';
        const priority = document.getElementById('priorityFilter')?.value || '';

        loadTickets(search, category, status, priority);
    }

    // 防抖函数
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});
</script>
{% endblock %}
