{% extends 'base.html' %}
{% load static %}

{% block title %}用户详情 - {{ user.name }}{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'user_lifecycle:lifecycle' %}">用户生命周期管理</a></li>
        <li class="breadcrumb-item active" aria-current="page">用户详情</li>
    </ol>
</nav>
{% endblock %}

{% block extra_css %}
<style>
    :root {
        /* 颜色系统 */
        --primary: hsl(221.2, 83.2%, 53.3%);
        --primary-light: hsl(221.2, 83.2%, 63.3%);
        --primary-dark: hsl(221.2, 83.2%, 43.3%);
        --secondary: hsl(210, 40%, 96%);
        --background: hsl(0, 0%, 100%);
        --surface: hsl(0, 0%, 100%);
        --foreground: hsl(222.2, 84%, 4.9%);
        --muted: hsl(210, 40%, 96%);
        --muted-foreground: hsl(215.4, 16.3%, 46.9%);
        --border: hsl(214.3, 31.8%, 91.4%);
        --success: hsl(142.1, 76.2%, 36.3%);
        --warning: hsl(38, 92%, 50%);
        --destructive: hsl(0, 84.2%, 60.2%);
        --info: hsl(199, 89%, 48%);

        /* 间距系统 */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
        --spacing-2xl: 3rem;

        /* 圆角系统 */
        --radius: 0.375rem;
        --radius-lg: 0.5rem;
        --radius-full: 9999px;

        /* 阴影系统 */
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    }

    /* 页面布局 */
    .user-detail-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
    }

    /* 用户基本信息卡片 */
    .user-info-card {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        border: 1px solid var(--border);
        position: relative;
        overflow: hidden;
    }

    .user-info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--primary-light), var(--info));
    }

    .user-header {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }

    .user-avatar {
        position: relative;
        flex-shrink: 0;
    }

    .user-avatar img {
        width: 80px;
        height: 80px;
        border-radius: var(--radius-full);
        border: 3px solid var(--border);
    }

    .status-badge {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 20px;
        height: 20px;
        border-radius: var(--radius-full);
        border: 3px solid var(--surface);
    }

    .status-active {
        background: var(--success);
    }

    .status-risk {
        background: var(--warning);
    }

    .status-churned {
        background: var(--destructive);
    }

    .user-basic-info {
        flex: 1;
    }

    .user-name {
        font-size: 24px;
        font-weight: 700;
        color: var(--foreground);
        margin: 0 0 var(--spacing-sm) 0;
    }

    .user-id {
        color: var(--muted-foreground);
        font-size: 14px;
        margin: 0 0 var(--spacing-md) 0;
    }

    .vip-badge {
        display: inline-block;
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-full);
        font-size: 14px;
        font-weight: 600;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #92400e;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* 用户统计信息 */
    .user-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .stat-item {
        text-align: center;
        padding: var(--spacing-md);
        background: var(--muted);
        border-radius: var(--radius);
    }

    .stat-label {
        display: block;
        font-size: 12px;
        color: var(--muted-foreground);
        margin-bottom: var(--spacing-xs);
    }

    .stat-value {
        display: block;
        font-size: 18px;
        font-weight: 600;
        color: var(--foreground);
    }

    /* 生命周期信息卡片 */
    .lifecycle-info-card {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        border: 1px solid var(--border);
    }

    .card-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--foreground);
        margin: 0 0 var(--spacing-lg) 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .lifecycle-stage {
        text-align: center;
        margin-bottom: var(--spacing-lg);
    }

    .stage-label {
        font-size: 14px;
        color: var(--muted-foreground);
        margin-bottom: var(--spacing-sm);
    }

    .stage-value {
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-full);
        font-size: 16px;
        font-weight: 600;
        display: inline-block;
    }

    .stage-growth {
        background: #dcfce7;
        color: #166534;
    }

    .stage-mature {
        background: #dbeafe;
        color: #1e40af;
    }

    .stage-decline {
        background: #fed7d7;
        color: #c53030;
    }

    /* 活跃度进度条 */
    .activity-progress {
        margin-bottom: var(--spacing-lg);
    }

    .progress-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);
    }

    .progress-bar {
        width: 100%;
        height: 12px;
        background: var(--muted);
        border-radius: var(--radius-full);
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: var(--primary);
        border-radius: var(--radius-full);
        transition: width 0.3s ease;
    }

    .progress-fill.risk {
        background: var(--warning);
    }

    /* 用户标签 */
    .user-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .tag {
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius);
        font-size: 12px;
        font-weight: 500;
    }

    .tag-high-value {
        background: #fef3c7;
        color: #92400e;
    }

    .tag-stable {
        background: #dcfce7;
        color: #166534;
    }

    .tag-social {
        background: #dbeafe;
        color: #1e40af;
    }

    .tag-risk {
        background: #fed7d7;
        color: #c53030;
    }

    .tag-potential {
        background: #e0e7ff;
        color: #3730a3;
    }

    .tag-growing {
        background: #dcfce7;
        color: #166534;
    }

    /* 详细数据区域 */
    .detail-section {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        border: 1px solid var(--border);
        margin-bottom: var(--spacing-xl);
    }

    .detail-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-lg);
    }

    .detail-item {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .detail-label {
        font-size: 12px;
        color: var(--muted-foreground);
        font-weight: 500;
    }

    .detail-value {
        font-size: 14px;
        color: var(--foreground);
        font-weight: 600;
    }

    /* 操作按钮区域 */
    .action-buttons {
        display: flex;
        gap: var(--spacing-md);
        margin-top: var(--spacing-xl);
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .btn-primary {
        background: var(--primary);
        color: white;
        border: 1px solid var(--primary);
    }

    .btn-primary:hover {
        background: var(--primary-dark);
        border-color: var(--primary-dark);
    }

    .btn-secondary {
        background: var(--surface);
        color: var(--foreground);
        border: 1px solid var(--border);
    }

    .btn-secondary:hover {
        background: var(--muted);
    }

    .btn-danger {
        background: var(--destructive);
        color: white;
        border: 1px solid var(--destructive);
    }

    .btn-danger:hover {
        background: #dc2626;
        border-color: #dc2626;
    }

    /* 充值记录样式 */
    .recharge-records, .login-records {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .recharge-item, .login-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-md);
        background: var(--muted);
        border-radius: var(--radius);
        border-left: 3px solid var(--primary);
    }

    .recharge-info, .login-info {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .recharge-amount {
        font-size: 16px;
        font-weight: 600;
        color: var(--success);
    }

    .recharge-product, .login-duration {
        font-size: 12px;
        color: var(--muted-foreground);
    }

    .recharge-time, .login-time {
        font-size: 14px;
        color: var(--foreground);
        font-weight: 500;
    }

    .login-device {
        font-size: 12px;
        color: var(--muted-foreground);
        padding: var(--spacing-xs) var(--spacing-sm);
        background: var(--surface);
        border-radius: var(--radius);
    }

    .empty-records {
        text-align: center;
        padding: var(--spacing-xl);
        color: var(--muted-foreground);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .empty-records i {
        font-size: 24px;
        opacity: 0.5;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .user-detail-container {
            grid-template-columns: 1fr;
            gap: var(--spacing-lg);
        }

        .user-header {
            flex-direction: column;
            text-align: center;
        }

        .user-stats {
            grid-template-columns: 1fr;
        }

        .detail-grid {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            flex-direction: column;
        }

        .recharge-item, .login-item {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-sm);
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- 用户详情主要内容 -->
<div class="user-detail-container">
    <!-- 用户基本信息 -->
    <div class="user-info-card">
        <div class="user-header">
            <div class="user-avatar">
                <img src="https://api.dicebear.com/7.x/avataaars/svg?seed={{ user.id }}" alt="用户头像">
                <div class="status-badge status-{{ user.status }}"></div>
            </div>
            <div class="user-basic-info">
                <h1 class="user-name">{{ user.name }}</h1>
                <p class="user-id">ID: {{ user.id }}</p>
                <div class="vip-badge">VIP {{ user.vip_level }}</div>
            </div>
        </div>

        <div class="user-stats">
            <div class="stat-item">
                <span class="stat-label">累计充值</span>
                <span class="stat-value">¥{{ user.total_recharge|floatformat:0 }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最后登录</span>
                <span class="stat-value">{{ user.last_login_display }}</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">生命周期</span>
                <span class="stat-value">{{ user.lifecycle_days }}天</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">注册时间</span>
                <span class="stat-value">{{ user.register_date }}</span>
            </div>
        </div>
    </div>

    <!-- 生命周期信息 -->
    <div class="lifecycle-info-card">
        <h2 class="card-title">
            <i class="bi bi-graph-up-arrow"></i>
            生命周期信息
        </h2>

        <div class="lifecycle-stage">
            <div class="stage-label">当前阶段</div>
            <span class="stage-value stage-{{ user.lifecycle_stage }}">{{ user.lifecycle_stage_display }}</span>
        </div>

        <div class="activity-progress">
            <div class="progress-label">
                <span>活跃度</span>
                <span>{{ user.activity_score }}%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill {% if user.activity_score < 30 %}risk{% endif %}" 
                     style="width: {{ user.activity_score }}%"></div>
            </div>
        </div>

        <div class="user-tags">
            {% for tag in user.tags %}
            <span class="tag tag-{{ tag.type }}">{{ tag.name }}</span>
            {% endfor %}
        </div>
    </div>
</div>

<!-- 详细数据区域 -->
<div class="detail-section">
    <h2 class="card-title">
        <i class="bi bi-bar-chart"></i>
        详细数据
    </h2>
    <div class="detail-grid">
        <div class="detail-item">
            <span class="detail-label">游戏等级</span>
            <span class="detail-value">{{ user.game_level }}</span>
        </div>
        <div class="detail-item">
            <span class="detail-label">战力值</span>
            <span class="detail-value">{{ user.combat_power|floatformat:0 }}</span>
        </div>
        <div class="detail-item">
            <span class="detail-label">公会</span>
            <span class="detail-value">{{ user.guild_name|default:"无" }}</span>
        </div>
        <div class="detail-item">
            <span class="detail-label">服务器</span>
            <span class="detail-value">{{ user.server_name }}</span>
        </div>
        <div class="detail-item">
            <span class="detail-label">本月充值</span>
            <span class="detail-value">¥{{ user.monthly_recharge|floatformat:0 }}</span>
        </div>
        <div class="detail-item">
            <span class="detail-label">本月登录天数</span>
            <span class="detail-value">{{ user.monthly_login_days }}天</span>
        </div>
        <div class="detail-item">
            <span class="detail-label">平均在线时长</span>
            <span class="detail-value">{{ user.avg_online_hours }}小时/天</span>
        </div>
        <div class="detail-item">
            <span class="detail-label">社交活跃度</span>
            <span class="detail-value">{{ user.social_activity_score }}分</span>
        </div>
    </div>
</div>

<!-- 充值记录区域 -->
<div class="detail-section">
    <h2 class="card-title">
        <i class="bi bi-credit-card"></i>
        近期充值记录
    </h2>
    <div class="recharge-records">
        {% for record in user.recent_recharges %}
        <div class="recharge-item">
            <div class="recharge-info">
                <span class="recharge-amount">¥{{ record.amount }}</span>
                <span class="recharge-product">{{ record.product_name }}</span>
            </div>
            <div class="recharge-time">{{ record.created_at }}</div>
        </div>
        {% empty %}
        <div class="empty-records">
            <i class="bi bi-inbox"></i>
            <span>暂无充值记录</span>
        </div>
        {% endfor %}
    </div>
</div>

<!-- 登录记录区域 -->
<div class="detail-section">
    <h2 class="card-title">
        <i class="bi bi-clock-history"></i>
        近期登录记录
    </h2>
    <div class="login-records">
        {% for record in user.recent_logins %}
        <div class="login-item">
            <div class="login-info">
                <span class="login-time">{{ record.login_time }}</span>
                <span class="login-duration">在线{{ record.duration }}小时</span>
            </div>
            <div class="login-device">{{ record.device_type }}</div>
        </div>
        {% empty %}
        <div class="empty-records">
            <i class="bi bi-inbox"></i>
            <span>暂无登录记录</span>
        </div>
        {% endfor %}
    </div>
</div>

<!-- 操作按钮区域 -->
<div class="action-buttons">
    <a href="{% url 'user_lifecycle:lifecycle' %}" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i>
        返回列表
    </a>
    <button class="btn btn-primary" onclick="editUser()">
        <i class="bi bi-pencil"></i>
        编辑用户
    </button>
    <button class="btn btn-primary" onclick="sendMessage()">
        <i class="bi bi-envelope"></i>
        发送消息
    </button>
    <button class="btn btn-danger" onclick="confirmAction('流失预警')">
        <i class="bi bi-exclamation-triangle"></i>
        标记风险
    </button>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 用户详情页面JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 用户详情页面加载');

        // 初始化页面
        initializePage();
    });

    function initializePage() {
        // 添加页面加载动画
        animateElements();

        console.log('✅ 用户详情页面初始化完成');
    }

    function animateElements() {
        // 为卡片添加进入动画
        const cards = document.querySelectorAll('.user-info-card, .lifecycle-info-card, .detail-section');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    function editUser() {
        // 编辑用户功能
        console.log('编辑用户');
        alert('编辑用户功能开发中...');
    }

    function sendMessage() {
        // 发送消息功能
        console.log('发送消息');
        alert('发送消息功能开发中...');
    }

    function confirmAction(action) {
        // 确认操作
        if (confirm(`确定要执行"${action}"操作吗？`)) {
            console.log(`执行操作: ${action}`);
            alert(`${action}操作已执行`);
        }
    }

    // 添加键盘快捷键支持
    document.addEventListener('keydown', function(e) {
        // ESC键返回列表
        if (e.key === 'Escape') {
            window.location.href = "{% url 'user_lifecycle:lifecycle' %}";
        }

        // Ctrl+E 编辑用户
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            editUser();
        }
    });
</script>
{% endblock %}
