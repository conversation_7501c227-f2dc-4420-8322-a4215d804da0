{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block breadcrumb %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    :root {
        /* 颜色系统 */
        --primary: hsl(221.2, 83.2%, 53.3%);
        --primary-light: hsl(221.2, 83.2%, 63.3%);
        --primary-dark: hsl(221.2, 83.2%, 43.3%);
        --secondary: hsl(210, 40%, 96%);
        --background: hsl(0, 0%, 100%);
        --surface: hsl(0, 0%, 100%);
        --foreground: hsl(222.2, 84%, 4.9%);
        --muted: hsl(210, 40%, 96%);
        --muted-foreground: hsl(215.4, 16.3%, 46.9%);
        --border: hsl(214.3, 31.8%, 91.4%);
        --input: hsl(214.3, 31.8%, 91.4%);
        --accent: hsl(210, 40%, 96%);
        --accent-foreground: hsl(222.2, 84%, 4.9%);
        --destructive: hsl(0, 84.2%, 60.2%);
        --destructive-foreground: hsl(210, 40%, 98%);
        --success: hsl(142.1, 76.2%, 36.3%);
        --warning: hsl(38, 92%, 50%);
        --info: hsl(199, 89%, 48%);

        /* 间距系统 */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
        --spacing-2xl: 3rem;

        /* 圆角系统 */
        --radius: 0.375rem;
        --radius-lg: 0.5rem;
        --radius-full: 9999px;

        /* 阴影系统 */
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    }

    /* 页面头部 */
    .page-header {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        border: 1px solid var(--border);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary), var(--primary-light), var(--info));
    }

    .page-title {
        font-size: 28px;
        font-weight: 700;
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: var(--spacing-sm);
    }

    .page-description {
        color: var(--muted-foreground);
        font-size: 16px;
    }

    /* 搜索和筛选区域 */
    .search-filter-section {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        border: 1px solid var(--border);
    }

    .search-bar {
        position: relative;
        margin-bottom: var(--spacing-lg);
    }

    .search-input {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 2.5rem;
        border: 1px solid var(--border);
        border-radius: var(--radius);
        font-size: 14px;
        background: var(--background);
        transition: all 0.2s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .search-icon {
        position: absolute;
        left: var(--spacing-md);
        top: 50%;
        transform: translateY(-50%);
        color: var(--muted-foreground);
    }

    .filter-tabs {
        display: flex;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
    }

    .filter-tab {
        padding: var(--spacing-sm) var(--spacing-md);
        border: 1px solid var(--border);
        border-radius: var(--radius);
        background: var(--background);
        color: var(--muted-foreground);
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
    }

    .filter-tab:hover {
        background: var(--accent);
    }

    .filter-tab.active {
        background: var(--primary);
        color: white;
        border-color: var(--primary);
    }

    /* 用户列表 */
    .users-list {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-xl);
    }

    .user-item {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        border: 1px solid var(--border);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);
    }

    .user-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(180deg, var(--primary), var(--primary-light));
    }

    .user-item[data-status="risk"]::before {
        background: linear-gradient(180deg, #f59e0b, #fbbf24);
    }

    .user-item[data-status="churned"]::before {
        background: linear-gradient(180deg, #ef4444, #f87171);
    }

    .user-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    /* 用户主要信息区域 */
    .user-main-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        min-width: 200px;
        flex-shrink: 0;
    }

    .user-avatar {
        position: relative;
        flex-shrink: 0;
    }

    .user-avatar img {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        border: 2px solid var(--border);
    }

    .status-badge {
        position: absolute;
        bottom: -2px;
        right: -2px;
        width: 14px;
        height: 14px;
        border-radius: var(--radius-full);
        border: 2px solid var(--surface);
    }

    .status-active {
        background: var(--success);
    }

    .status-risk {
        background: var(--warning);
    }

    .status-churned {
        background: var(--destructive);
    }

    .user-basic {
        flex: 1;
    }

    .user-name {
        font-size: 16px;
        font-weight: 600;
        color: var(--foreground);
        margin: 0 0 4px 0;
    }

    .user-id {
        color: var(--muted-foreground);
        font-size: 13px;
        margin: 0 0 6px 0;
    }

    .vip-badge {
        display: inline-block;
        padding: 2px 8px;
        border-radius: var(--radius-full);
        font-size: 11px;
        font-weight: 600;
        background: linear-gradient(135deg, #ffd700, #ffed4e);
        color: #92400e;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* 用户统计行 */
    .user-stats-row {
        display: flex;
        gap: var(--spacing-lg);
        min-width: 300px;
        flex-shrink: 0;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-label {
        display: block;
        font-size: 11px;
        color: var(--muted-foreground);
        margin-bottom: 2px;
    }

    .stat-value {
        display: block;
        font-size: 13px;
        font-weight: 600;
        color: var(--foreground);
    }

    /* 生命周期行 */
    .user-lifecycle-row {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        min-width: 250px;
        flex-shrink: 0;
    }

    .lifecycle-stage {
        display: flex;
        flex-direction: column;
        align-items: center;
        min-width: 80px;
    }

    .stage-label {
        font-size: 11px;
        color: var(--muted-foreground);
        margin-bottom: 2px;
    }

    .stage-value {
        padding: 2px 8px;
        border-radius: var(--radius-full);
        font-size: 11px;
        font-weight: 600;
    }

    .stage-growth {
        background: #dcfce7;
        color: #166534;
    }

    .stage-mature {
        background: #dbeafe;
        color: #1e40af;
    }

    .stage-decline {
        background: #fed7d7;
        color: #c53030;
    }

    .lifecycle-progress {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        flex: 1;
    }

    .progress-bar {
        flex: 1;
        height: 6px;
        background: var(--muted);
        border-radius: var(--radius-full);
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: var(--primary);
        border-radius: var(--radius-full);
        transition: width 0.3s ease;
    }

    .progress-fill.risk {
        background: var(--warning);
    }

    .progress-text {
        font-size: 11px;
        color: var(--muted-foreground);
        white-space: nowrap;
        min-width: 60px;
    }

    /* 用户标签行 */
    .user-tags-row {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-xs);
        min-width: 150px;
        flex-shrink: 0;
    }

    .tag {
        padding: 2px 6px;
        border-radius: var(--radius);
        font-size: 10px;
        font-weight: 500;
    }

    .tag-high-value {
        background: #fef3c7;
        color: #92400e;
    }

    .tag-stable {
        background: #dcfce7;
        color: #166534;
    }

    .tag-social {
        background: #dbeafe;
        color: #1e40af;
    }

    .tag-risk {
        background: #fed7d7;
        color: #c53030;
    }

    .tag-declining {
        background: #fef3c7;
        color: #92400e;
    }

    .tag-potential {
        background: #e0e7ff;
        color: #3730a3;
    }

    .tag-growing {
        background: #dcfce7;
        color: #166534;
    }

    /* 用户操作区域 */
    .user-actions {
        display: flex;
        gap: var(--spacing-xs);
        flex-shrink: 0;
    }

    .btn-icon {
        width: 28px;
        height: 28px;
        border: none;
        background: var(--muted);
        color: var(--muted-foreground);
        border-radius: var(--radius);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }

    .btn-icon:hover {
        background: var(--primary);
        color: white;
    }

    /* 分页组件 */
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: var(--spacing-sm);
        margin-top: var(--spacing-xl);
    }

    .pagination-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        border: 1px solid var(--border);
        border-radius: var(--radius);
        background: var(--background);
        color: var(--foreground);
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
    }

    .pagination-btn:hover {
        background: var(--accent);
    }

    .pagination-btn.active {
        background: var(--primary);
        color: white;
        border-color: var(--primary);
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
        .user-item {
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }
        
        .user-stats-row {
            min-width: auto;
            order: 3;
            width: 100%;
        }
        
        .user-lifecycle-row {
            min-width: auto;
            order: 4;
            width: 100%;
        }
        
        .user-tags-row {
            min-width: auto;
            order: 5;
            width: 100%;
        }
    }

    @media (max-width: 768px) {
        .user-item {
            flex-direction: column;
            align-items: stretch;
            gap: var(--spacing-md);
        }
        
        .user-main-info {
            min-width: auto;
            width: 100%;
            justify-content: space-between;
        }
        
        .user-stats-row {
            justify-content: space-around;
        }
        
        .user-lifecycle-row {
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .lifecycle-progress {
            width: 100%;
        }
        
        .user-actions {
            justify-content: center;
        }

        .filter-tabs {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- 页面头部 -->
<div class="page-header">
    <h1 class="page-title">{{ page_title }}</h1>
    <p class="page-description">{{ page_description }}</p>
</div>

<!-- 搜索和筛选区域 -->
<div class="search-filter-section">
    <div class="search-bar">
        <i class="bi bi-search search-icon"></i>
        <input type="text" class="search-input" id="userSearch" placeholder="搜索用户名或ID...">
    </div>
    
    <div class="filter-tabs">
        <div class="filter-tab active" data-filter="all">全部用户</div>
        <div class="filter-tab" data-filter="active">活跃用户</div>
        <div class="filter-tab" data-filter="risk">流失风险</div>
        <div class="filter-tab" data-filter="churned">已流失</div>
    </div>
</div>

<!-- 用户列表 -->
<div class="users-list">
    <!-- 用户列表项示例 -->
    <div class="user-item" data-status="active">
        <div class="user-main-info">
            <div class="user-avatar">
                <img src="https://api.dicebear.com/7.x/avataaars/svg?seed=user1" alt="用户头像">
                <div class="status-badge status-active"></div>
            </div>
            <div class="user-basic">
                <h3 class="user-name">龙傲天</h3>
                <p class="user-id">ID: 100001</p>
                <div class="user-vip">
                    <span class="vip-badge">VIP 10</span>
                </div>
            </div>
        </div>
        
        <div class="user-stats-row">
            <div class="stat-item">
                <span class="stat-label">累计充值</span>
                <span class="stat-value">¥128,500</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最后登录</span>
                <span class="stat-value">2小时前</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">生命周期</span>
                <span class="stat-value">365天</span>
            </div>
        </div>
        
        <div class="user-lifecycle-row">
            <div class="lifecycle-stage">
                <span class="stage-label">当前阶段</span>
                <span class="stage-value stage-mature">成熟期</span>
            </div>
            <div class="lifecycle-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 75%"></div>
                </div>
                <span class="progress-text">活跃度 75%</span>
            </div>
        </div>
        
        <div class="user-tags-row">
            <span class="tag tag-high-value">高价值</span>
            <span class="tag tag-stable">稳定</span>
            <span class="tag tag-social">社交活跃</span>
        </div>
        
        <div class="user-actions">
            <button class="btn-icon" title="查看详情">
                <i class="bi bi-eye"></i>
            </button>
            <button class="btn-icon" title="编辑用户">
                <i class="bi bi-pencil"></i>
            </button>
            <button class="btn-icon" title="更多操作">
                <i class="bi bi-three-dots"></i>
            </button>
        </div>
    </div>

    <!-- 更多用户列表项 -->
    <div class="user-item" data-status="risk">
        <div class="user-main-info">
            <div class="user-avatar">
                <img src="https://api.dicebear.com/7.x/avataaars/svg?seed=user2" alt="用户头像">
                <div class="status-badge status-risk"></div>
            </div>
            <div class="user-basic">
                <h3 class="user-name">剑神无双</h3>
                <p class="user-id">ID: 100002</p>
                <div class="user-vip">
                    <span class="vip-badge">VIP 8</span>
                </div>
            </div>
        </div>
        
        <div class="user-stats-row">
            <div class="stat-item">
                <span class="stat-label">累计充值</span>
                <span class="stat-value">¥45,200</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最后登录</span>
                <span class="stat-value">7天前</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">生命周期</span>
                <span class="stat-value">180天</span>
            </div>
        </div>
        
        <div class="user-lifecycle-row">
            <div class="lifecycle-stage">
                <span class="stage-label">当前阶段</span>
                <span class="stage-value stage-decline">衰退期</span>
            </div>
            <div class="lifecycle-progress">
                <div class="progress-bar">
                    <div class="progress-fill risk" style="width: 25%"></div>
                </div>
                <span class="progress-text">活跃度 25%</span>
            </div>
        </div>
        
        <div class="user-tags-row">
            <span class="tag tag-risk">流失风险</span>
            <span class="tag tag-declining">活跃下降</span>
        </div>
        
        <div class="user-actions">
            <button class="btn-icon" title="查看详情">
                <i class="bi bi-eye"></i>
            </button>
            <button class="btn-icon" title="编辑用户">
                <i class="bi bi-pencil"></i>
            </button>
            <button class="btn-icon" title="更多操作">
                <i class="bi bi-three-dots"></i>
            </button>
        </div>
    </div>

    <div class="user-item" data-status="active">
        <div class="user-main-info">
            <div class="user-avatar">
                <img src="https://api.dicebear.com/7.x/avataaars/svg?seed=user3" alt="用户头像">
                <div class="status-badge status-active"></div>
            </div>
            <div class="user-basic">
                <h3 class="user-name">花间一壶酒</h3>
                <p class="user-id">ID: 100003</p>
                <div class="user-vip">
                    <span class="vip-badge">VIP 6</span>
                </div>
            </div>
        </div>
        
        <div class="user-stats-row">
            <div class="stat-item">
                <span class="stat-label">累计充值</span>
                <span class="stat-value">¥18,900</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最后登录</span>
                <span class="stat-value">30分钟前</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">生命周期</span>
                <span class="stat-value">90天</span>
            </div>
        </div>
        
        <div class="user-lifecycle-row">
            <div class="lifecycle-stage">
                <span class="stage-label">当前阶段</span>
                <span class="stage-value stage-growth">成长期</span>
            </div>
            <div class="lifecycle-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 60%"></div>
                </div>
                <span class="progress-text">活跃度 60%</span>
            </div>
        </div>
        
        <div class="user-tags-row">
            <span class="tag tag-potential">潜力用户</span>
            <span class="tag tag-growing">快速成长</span>
        </div>
        
        <div class="user-actions">
            <button class="btn-icon" title="查看详情">
                <i class="bi bi-eye"></i>
            </button>
            <button class="btn-icon" title="编辑用户">
                <i class="bi bi-pencil"></i>
            </button>
            <button class="btn-icon" title="更多操作">
                <i class="bi bi-three-dots"></i>
            </button>
        </div>
    </div>

    <div class="user-item" data-status="churned">
        <div class="user-main-info">
            <div class="user-avatar">
                <img src="https://api.dicebear.com/7.x/avataaars/svg?seed=user4" alt="用户头像">
                <div class="status-badge status-churned"></div>
            </div>
            <div class="user-basic">
                <h3 class="user-name">独孤求败</h3>
                <p class="user-id">ID: 100004</p>
                <div class="user-vip">
                    <span class="vip-badge">VIP 5</span>
                </div>
            </div>
        </div>
        
        <div class="user-stats-row">
            <div class="stat-item">
                <span class="stat-label">累计充值</span>
                <span class="stat-value">¥12,300</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">最后登录</span>
                <span class="stat-value">30天前</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">生命周期</span>
                <span class="stat-value">120天</span>
            </div>
        </div>
        
        <div class="user-lifecycle-row">
            <div class="lifecycle-stage">
                <span class="stage-label">当前阶段</span>
                <span class="stage-value stage-decline">已流失</span>
            </div>
            <div class="lifecycle-progress">
                <div class="progress-bar">
                    <div class="progress-fill risk" style="width: 0%"></div>
                </div>
                <span class="progress-text">活跃度 0%</span>
            </div>
        </div>
        
        <div class="user-tags-row">
            <span class="tag tag-risk">已流失</span>
            <span class="tag tag-declining">需要挽回</span>
        </div>
        
        <div class="user-actions">
            <button class="btn-icon" title="查看详情">
                <i class="bi bi-eye"></i>
            </button>
            <button class="btn-icon" title="编辑用户">
                <i class="bi bi-pencil"></i>
            </button>
            <button class="btn-icon" title="更多操作">
                <i class="bi bi-three-dots"></i>
            </button>
        </div>
    </div>
</div>

<!-- 分页组件 -->
<div class="pagination">
    <button class="pagination-btn" disabled>
        <i class="bi bi-chevron-left"></i>
    </button>
    <a href="#" class="pagination-btn active">1</a>
    <a href="#" class="pagination-btn">2</a>
    <a href="#" class="pagination-btn">3</a>
    <span class="pagination-btn">...</span>
    <a href="#" class="pagination-btn">10</a>
    <button class="pagination-btn">
        <i class="bi bi-chevron-right"></i>
    </button>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 筛选标签切换
    const filterTabs = document.querySelectorAll('.filter-tab');
    const userItems = document.querySelectorAll('.user-item');
    
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有活跃状态
            filterTabs.forEach(t => t.classList.remove('active'));
            // 添加当前活跃状态
            this.classList.add('active');
            
            const filter = this.dataset.filter;
            
            // 筛选用户列表项
            userItems.forEach(item => {
                if (filter === 'all') {
                    item.style.display = 'flex';
                } else {
                    const status = item.dataset.status;
                    item.style.display = status === filter ? 'flex' : 'none';
                }
            });
        });
    });
    
    // 搜索功能
    const searchInput = document.getElementById('userSearch');
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        userItems.forEach(item => {
            const userName = item.querySelector('.user-name').textContent.toLowerCase();
            const userId = item.querySelector('.user-id').textContent.toLowerCase();
            
            if (userName.includes(searchTerm) || userId.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // 用户操作按钮事件
    document.querySelectorAll('.btn-icon').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const action = this.getAttribute('title');
            const userItem = this.closest('.user-item');
            const userName = userItem.querySelector('.user-name').textContent;
            
            console.log(`${action}: ${userName}`);
            // 这里可以添加具体的操作逻辑
        });
    });
</script>
{% endblock %}