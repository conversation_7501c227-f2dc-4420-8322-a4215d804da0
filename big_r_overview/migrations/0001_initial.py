# Generated by Django 5.2.4 on 2025-08-05 09:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BigROverviewStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stat_date', models.DateField(unique=True, verbose_name='统计日期')),
                ('total_big_r_users', models.IntegerField(default=0, verbose_name='大R用户总数')),
                ('total_potential_users', models.IntegerField(default=0, verbose_name='潜力用户数量')),
                ('total_churn_warning_users', models.IntegerField(default=0, verbose_name='流失预警用户数')),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='总收入')),
                ('avg_arpu', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='平均ARPU值')),
                ('vip_distribution', models.JSONField(default=dict, verbose_name='VIP等级分布')),
                ('new_big_r_users', models.IntegerField(default=0, verbose_name='新增大R用户')),
                ('lost_big_r_users', models.IntegerField(default=0, verbose_name='流失大R用户')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '大R总览统计',
                'verbose_name_plural': '大R总览统计',
                'ordering': ['-stat_date'],
            },
        ),
        migrations.CreateModel(
            name='BigRUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.BigIntegerField(unique=True, verbose_name='用户ID')),
                ('username', models.CharField(max_length=100, verbose_name='用户名')),
                ('character_name', models.CharField(max_length=100, verbose_name='角色名')),
                ('server_id', models.IntegerField(verbose_name='服务器ID')),
                ('server_name', models.CharField(max_length=50, verbose_name='服务器名称')),
                ('total_recharge', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='累计充值金额')),
                ('first_recharge_date', models.DateTimeField(blank=True, null=True, verbose_name='首次充值时间')),
                ('last_recharge_date', models.DateTimeField(blank=True, null=True, verbose_name='最后充值时间')),
                ('last_login_date', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('total_login_days', models.IntegerField(default=0, verbose_name='累计登录天数')),
                ('consecutive_login_days', models.IntegerField(default=0, verbose_name='连续登录天数')),
                ('is_potential', models.BooleanField(default=False, verbose_name='是否为潜力用户')),
                ('is_churn_risk', models.BooleanField(default=False, verbose_name='是否有流失风险')),
                ('churn_risk_level', models.CharField(choices=[('low', '低风险'), ('medium', '中风险'), ('high', '高风险'), ('critical', '严重风险')], default='low', max_length=10, verbose_name='流失风险等级')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '大R用户',
                'verbose_name_plural': '大R用户',
                'indexes': [models.Index(fields=['user_id'], name='big_r_overv_user_id_9d5bff_idx'), models.Index(fields=['total_recharge'], name='big_r_overv_total_r_31ab81_idx'), models.Index(fields=['last_login_date'], name='big_r_overv_last_lo_c139ca_idx'), models.Index(fields=['is_potential'], name='big_r_overv_is_pote_0a7d31_idx'), models.Index(fields=['is_churn_risk'], name='big_r_overv_is_chur_a46ce8_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserBehaviorAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('avg_recharge_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='平均充值金额')),
                ('recharge_frequency', models.FloatField(default=0, verbose_name='充值频率(次/月)')),
                ('days_since_last_recharge', models.IntegerField(default=0, verbose_name='距离上次充值天数')),
                ('avg_online_duration', models.IntegerField(default=0, verbose_name='平均在线时长(分钟/天)')),
                ('login_frequency', models.FloatField(default=0, verbose_name='登录频率(天/周)')),
                ('days_since_last_login', models.IntegerField(default=0, verbose_name='距离上次登录天数')),
                ('potential_score', models.FloatField(default=0, verbose_name='潜力评分(0-100)')),
                ('churn_risk_score', models.FloatField(default=0, verbose_name='流失风险评分(0-100)')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='big_r_overview.bigruser', verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户行为分析',
                'verbose_name_plural': '用户行为分析',
            },
        ),
        migrations.CreateModel(
            name='UserLoginRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('login_date', models.DateField(verbose_name='登录日期')),
                ('login_time', models.DateTimeField(verbose_name='登录时间')),
                ('online_duration', models.IntegerField(default=0, verbose_name='在线时长(分钟)')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('device_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='设备类型')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='big_r_overview.bigruser', verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户登录记录',
                'verbose_name_plural': '用户登录记录',
                'indexes': [models.Index(fields=['user', 'login_date'], name='big_r_overv_user_id_f1a94a_idx'), models.Index(fields=['login_date'], name='big_r_overv_login_d_4bf08c_idx')],
                'unique_together': {('user', 'login_date')},
            },
        ),
        migrations.CreateModel(
            name='UserRechargeRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_id', models.CharField(max_length=100, unique=True, verbose_name='订单号')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='充值金额')),
                ('currency', models.CharField(default='CNY', max_length=10, verbose_name='货币类型')),
                ('payment_method', models.CharField(max_length=50, verbose_name='支付方式')),
                ('status', models.CharField(choices=[('pending', '待支付'), ('success', '支付成功'), ('failed', '支付失败'), ('refunded', '已退款')], default='pending', max_length=20, verbose_name='支付状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='支付时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='big_r_overview.bigruser', verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户充值记录',
                'verbose_name_plural': '用户充值记录',
                'indexes': [models.Index(fields=['user', 'created_at'], name='big_r_overv_user_id_8ca442_idx'), models.Index(fields=['status'], name='big_r_overv_status_e1da1b_idx'), models.Index(fields=['paid_at'], name='big_r_overv_paid_at_903b46_idx')],
            },
        ),
    ]
