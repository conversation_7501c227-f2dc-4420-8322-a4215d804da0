from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
import random
from decimal import Decimal

from big_r_overview.models import BigRUser, UserRechargeRecord, UserLoginRecord
from big_r_overview.services import UserClassificationService, StatsGenerationService


class Command(BaseCommand):
    help = '初始化大R用户示例数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新创建数据，删除现有数据',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始初始化大R用户示例数据...'))

        # 检查是否已有数据
        if BigRUser.objects.exists() and not options['force']:
            self.stdout.write(self.style.WARNING('数据库中已有大R用户数据，使用 --force 参数强制重新创建'))
            return

        if options['force']:
            self.stdout.write('强制删除现有数据...')
            BigRUser.objects.all().delete()
            UserRechargeRecord.objects.all().delete()
            UserLoginRecord.objects.all().delete()
        
        # 创建示例用户
        self.create_users()
        
        # 创建充值记录
        self.create_recharge_records()
        
        # 创建登录记录
        self.create_login_records()
        
        # 执行用户分类
        self.classify_users()
        
        # 生成统计数据
        self.generate_stats()
        
        self.stdout.write(self.style.SUCCESS('大R用户示例数据初始化完成！'))

    def create_users(self):
        """创建示例用户"""
        self.stdout.write('创建示例用户...')
        
        # 用户名和角色名列表
        character_names = [
            '龙战天下', '剑指苍穹', '霸王无双', '风云再起', '神话传说',
            '王者归来', '至尊无敌', '傲视群雄', '独步天下', '笑傲江湖',
            '天下第一', '无敌战神', '绝世高手', '武林盟主', '江湖霸主',
            '超级玩家', '土豪大佬', '充值狂魔', '氪金战士', '人民币玩家',
            '游戏之王', '传奇玩家', '顶级大佬', '终极战士', '无敌神话',
            '巅峰王者', '极限挑战', '梦想成真', '荣耀之光', '胜利之师',
            '钢铁战士', '火焰之心', '冰雪女王', '雷电法王', '暗影刺客',
            '光明使者', '黑暗领主', '元素法师', '圣骑士', '死亡骑士',
            '魔法师', '弓箭手', '盗贼', '牧师', '战士',
            '法师', '猎人', '术士', '萨满', '德鲁伊'
        ]
        
        servers = [
            (1, '天启服'), (2, '创世服'), (3, '永恒服'), (4, '传奇服'), (5, '荣耀服'),
            (6, '王者服'), (7, '至尊服'), (8, '霸主服'), (9, '神话服'), (10, '无双服')
        ]
        
        users = []
        for i in range(100):  # 创建100个用户
            server_id, server_name = random.choice(servers)
            character_name = random.choice(character_names)
            
            # 生成不同充值水平的用户
            if i < 10:  # 10个超级大R
                total_recharge = Decimal(random.randint(50000, 200000))
            elif i < 30:  # 20个高级大R
                total_recharge = Decimal(random.randint(10000, 50000))
            elif i < 60:  # 30个中级大R
                total_recharge = Decimal(random.randint(2000, 10000))
            else:  # 40个普通用户
                total_recharge = Decimal(random.randint(100, 2000))
            
            # 生成注册时间（最近30天内，确保有增长趋势）
            days_ago = random.randint(1, 30)
            created_at = timezone.now() - timedelta(days=days_ago, hours=random.randint(0, 23), minutes=random.randint(0, 59))
            
            # 生成最后登录时间
            login_days_ago = random.randint(0, 30)
            last_login_date = timezone.now() - timedelta(days=login_days_ago)
            
            user = BigRUser(
                user_id=100000 + i,
                username=f"user_{100000 + i}",
                character_name=f"{character_name}_{i:03d}",
                server_id=server_id,
                server_name=server_name,
                total_recharge=total_recharge,
                first_recharge_date=created_at + timedelta(days=random.randint(1, 7)),
                last_recharge_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                last_login_date=last_login_date,
                total_login_days=random.randint(10, 150),
                consecutive_login_days=random.randint(1, 30),
                created_at=created_at
            )
            users.append(user)
        
        BigRUser.objects.bulk_create(users)
        self.stdout.write(f'创建了 {len(users)} 个用户')

    def create_recharge_records(self):
        """创建充值记录"""
        self.stdout.write('创建充值记录...')
        
        users = list(BigRUser.objects.all())
        records = []
        
        for user in users:
            # 根据用户充值总额生成充值记录
            remaining_amount = user.total_recharge
            record_count = random.randint(1, 10)
            
            for i in range(record_count):
                if remaining_amount <= 0:
                    break
                
                # 生成充值金额
                if i == record_count - 1:  # 最后一笔充值
                    amount = remaining_amount
                else:
                    max_amount = min(remaining_amount, user.total_recharge / record_count * 2)
                    min_amount = max(1, int(float(max_amount) * 0.1))
                    max_amount_int = max(min_amount, int(float(max_amount)))
                    amount = Decimal(random.randint(min_amount, max_amount_int))
                
                remaining_amount -= amount
                
                # 生成充值时间
                days_ago = random.randint(1, 60)
                paid_at = timezone.now() - timedelta(days=days_ago)
                
                record = UserRechargeRecord(
                    user=user,
                    order_id=f"ORDER_{user.user_id}_{i:03d}_{int(paid_at.timestamp())}",
                    amount=amount,
                    currency='CNY',
                    payment_method=random.choice(['支付宝', '微信支付', '银行卡', 'Apple Pay']),
                    status='success',
                    created_at=paid_at - timedelta(minutes=random.randint(1, 30)),
                    paid_at=paid_at
                )
                records.append(record)
        
        UserRechargeRecord.objects.bulk_create(records)
        self.stdout.write(f'创建了 {len(records)} 条充值记录')

    def create_login_records(self):
        """创建登录记录"""
        self.stdout.write('创建登录记录...')
        
        users = list(BigRUser.objects.all())
        records = []
        
        for user in users:
            # 为每个用户创建最近30天的登录记录
            for days_ago in range(30):
                # 随机决定是否登录（70%概率）
                if random.random() < 0.7:
                    login_date = timezone.now().date() - timedelta(days=days_ago)
                    login_time = timezone.now() - timedelta(
                        days=days_ago,
                        hours=random.randint(0, 23),
                        minutes=random.randint(0, 59)
                    )
                    
                    record = UserLoginRecord(
                        user=user,
                        login_date=login_date,
                        login_time=login_time,
                        online_duration=random.randint(30, 480),  # 30分钟到8小时
                        ip_address=f"192.168.{random.randint(1, 255)}.{random.randint(1, 255)}",
                        device_type=random.choice(['PC', 'Mobile', 'Tablet'])
                    )
                    records.append(record)
        
        # 批量创建，忽略重复键错误
        try:
            UserLoginRecord.objects.bulk_create(records, ignore_conflicts=True)
            self.stdout.write(f'创建了 {len(records)} 条登录记录')
        except Exception as e:
            self.stdout.write(f'登录记录创建出错: {e}')
            # 如果批量创建失败，尝试逐个创建
            created_count = 0
            for record in records:
                try:
                    record.save()
                    created_count += 1
                except:
                    pass  # 忽略重复键错误
            self.stdout.write(f'实际创建了 {created_count} 条登录记录')

    def classify_users(self):
        """执行用户分类"""
        self.stdout.write('执行用户分类...')
        UserClassificationService.update_user_data()
        self.stdout.write('用户分类完成')

    def generate_stats(self):
        """生成统计数据"""
        self.stdout.write('生成统计数据...')
        
        # 生成最近30天的统计数据
        for days_ago in range(30):
            target_date = timezone.now().date() - timedelta(days=days_ago)
            StatsGenerationService.generate_daily_stats(target_date)
        
        self.stdout.write('统计数据生成完成')
