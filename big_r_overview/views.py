from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from .services import BigROverviewService, UserClassificationService, StatsGenerationService


def big_r_overview(request):
    """大R用户总览页面视图"""
    context = {
        'page_title': '大R用户总览',
        'page_description': '系统主仪表盘，提供大R用户的宏观数据概览',
    }
    return render(request, 'big_r_overview/overview.html', context)


@require_http_methods(["GET"])
def metrics_api(request):
    """获取核心指标数据API"""
    try:
        data = BigROverviewService.get_metrics_data()
        return JsonResponse({
            'success': True,
            'data': data
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取指标数据失败: {str(e)}'
        }, status=500)


@require_http_methods(["GET"])
def growth_trend_api(request):
    """获取增长趋势数据API"""
    try:
        period = int(request.GET.get('period', 30))
        if period not in [7, 30, 90]:
            period = 30

        trend_type = request.GET.get('type', 'cumulative')
        if trend_type not in ['cumulative', 'daily']:
            trend_type = 'cumulative'

        trend_data = BigROverviewService.calculate_growth_trend(period, trend_type)

        # 格式化为图表数据
        labels = [item['date'] for item in trend_data]
        data = [item['count'] for item in trend_data]

        # 根据趋势类型设置标签
        if trend_type == 'daily':
            dataset_label = '每日新增用户'
        else:
            dataset_label = '累计用户数量'

        return JsonResponse({
            'success': True,
            'data': {
                'period': period,
                'trend_type': trend_type,
                'labels': labels,
                'datasets': [{
                    'label': dataset_label,
                    'data': data
                }]
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取趋势数据失败: {str(e)}'
        }, status=500)


@require_http_methods(["GET"])
def vip_distribution_api(request):
    """获取VIP分布数据API"""
    try:
        distribution = BigROverviewService.calculate_vip_distribution()
        return JsonResponse({
            'success': True,
            'data': distribution
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取VIP分布数据失败: {str(e)}'
        }, status=500)
