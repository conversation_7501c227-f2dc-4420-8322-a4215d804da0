from django.contrib import admin
from .models import BigRUser, UserRechargeRecord, UserLoginRecord, BigROverviewStats, UserBehaviorAnalysis


@admin.register(BigRUser)
class BigRUserAdmin(admin.ModelAdmin):
    list_display = ['user_id', 'character_name', 'server_name', 'vip_level', 'total_recharge', 'last_login_date', 'is_potential', 'is_churn_risk']
    list_filter = ['server_name', 'is_potential', 'is_churn_risk', 'churn_risk_level', 'created_at']
    search_fields = ['user_id', 'username', 'character_name']
    readonly_fields = ['created_at', 'updated_at', 'vip_level']
    ordering = ['-total_recharge']

    fieldsets = (
        ('基础信息', {
            'fields': ('user_id', 'username', 'character_name', 'server_id', 'server_name')
        }),
        ('充值信息', {
            'fields': ('total_recharge', 'first_recharge_date', 'last_recharge_date')
        }),
        ('活跃度信息', {
            'fields': ('last_login_date', 'total_login_days', 'consecutive_login_days')
        }),
        ('状态标识', {
            'fields': ('is_potential', 'is_churn_risk', 'churn_risk_level')
        }),
        ('时间戳', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(UserRechargeRecord)
class UserRechargeRecordAdmin(admin.ModelAdmin):
    list_display = ['order_id', 'user', 'amount', 'status', 'payment_method', 'paid_at']
    list_filter = ['status', 'payment_method', 'currency', 'created_at']
    search_fields = ['order_id', 'user__character_name', 'user__user_id']
    readonly_fields = ['created_at']
    ordering = ['-created_at']


@admin.register(UserLoginRecord)
class UserLoginRecordAdmin(admin.ModelAdmin):
    list_display = ['user', 'login_date', 'online_duration', 'device_type', 'ip_address']
    list_filter = ['login_date', 'device_type']
    search_fields = ['user__character_name', 'user__user_id', 'ip_address']
    ordering = ['-login_date']


@admin.register(BigROverviewStats)
class BigROverviewStatsAdmin(admin.ModelAdmin):
    list_display = ['stat_date', 'total_big_r_users', 'total_potential_users', 'total_churn_warning_users', 'avg_arpu']
    list_filter = ['stat_date']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-stat_date']


@admin.register(UserBehaviorAnalysis)
class UserBehaviorAnalysisAdmin(admin.ModelAdmin):
    list_display = ['user', 'potential_score', 'churn_risk_score', 'recharge_frequency', 'login_frequency']
    list_filter = ['updated_at']
    search_fields = ['user__character_name', 'user__user_id']
    readonly_fields = ['updated_at']
    ordering = ['-potential_score']
