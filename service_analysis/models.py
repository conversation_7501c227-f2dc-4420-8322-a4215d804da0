from django.db import models
from privileges.models import Privilege


class PrivilegeProcessRecord(models.Model):
    """特权处理记录模型"""

    # 角色信息
    character_name = models.CharField(max_length=100, verbose_name="角色名")
    character_id = models.IntegerField(verbose_name="角色ID")
    vip_level = models.IntegerField(default=0, verbose_name="VIP等级")

    # 特权信息
    privilege = models.ForeignKey(
        Privilege,
        on_delete=models.CASCADE,
        verbose_name="特权类型"
    )

    # 处理信息
    process_detail = models.TextField(verbose_name="处理详情")
    processor = models.CharField(max_length=100, verbose_name="处理人")
    processed_at = models.DateTimeField(auto_now_add=True, verbose_name="处理时间")

    class Meta:
        verbose_name = "特权处理记录"
        verbose_name_plural = "特权处理记录"
        ordering = ['-processed_at']
        indexes = [
            models.Index(fields=['character_id']),
            models.Index(fields=['vip_level']),
            models.Index(fields=['privilege']),
            models.Index(fields=['processed_at']),
            models.Index(fields=['processor']),
        ]

    def __str__(self):
        return f"{self.character_name}({self.character_id}) - {self.privilege.name}"
