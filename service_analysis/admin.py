from django.contrib import admin
from .models import PrivilegeProcessRecord


@admin.register(PrivilegeProcessRecord)
class PrivilegeProcessRecordAdmin(admin.ModelAdmin):
    list_display = ['character_name', 'character_id', 'vip_level', 'privilege', 'processor', 'processed_at']
    list_filter = ['vip_level', 'privilege', 'processor', 'processed_at']
    search_fields = ['character_name', 'character_id', 'process_detail', 'processor']
    ordering = ['-processed_at']
    readonly_fields = ['processed_at']

    fieldsets = (
        ('角色信息', {
            'fields': ('character_name', 'character_id', 'vip_level')
        }),
        ('特权信息', {
            'fields': ('privilege',)
        }),
        ('处理信息', {
            'fields': ('process_detail', 'processor', 'processed_at')
        }),
    )
