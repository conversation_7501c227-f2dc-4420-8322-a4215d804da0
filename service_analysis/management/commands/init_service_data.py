from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
import random

from service_analysis.models import PrivilegeProcessRecord
from privileges.models import Privilege


class Command(BaseCommand):
    help = '初始化特权处理记录示例数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始初始化特权处理记录示例数据...'))

        # 清空现有数据
        PrivilegeProcessRecord.objects.all().delete()

        # 创建示例处理记录
        self.create_process_records()

        self.stdout.write(self.style.SUCCESS('特权处理记录示例数据初始化完成！'))

    def create_process_records(self):
        """创建特权处理记录"""
        # 获取所有可用的特权
        privileges = list(Privilege.objects.filter(status='active'))
        if not privileges:
            self.stdout.write(self.style.ERROR('没有找到可用的特权，请先初始化特权数据'))
            return

        # 处理人列表
        processors = [
            '客服小王', '客服小李', '客服小张', '客服小陈', '客服小刘',
            '客服主管', '系统管理员', '运营专员', '技术支持'
        ]

        # 角色名列表
        character_names = [
            '龙战天下', '剑指苍穹', '霸王无双', '风云再起', '神话传说',
            '王者归来', '至尊无敌', '傲视群雄', '独步天下', '笑傲江湖',
            '天下第一', '无敌战神', '绝世高手', '武林盟主', '江湖霸主',
            '超级玩家', '土豪大佬', '充值狂魔', '氪金战士', '人民币玩家'
        ]

        # 处理详情模板
        process_details = [
            '用户申请开通VIP专属特权，已核实充值记录，特权开通成功',
            '用户反馈特权功能异常，经技术排查已修复，重新为用户开通',
            '用户误操作导致特权失效，已恢复用户特权状态',
            '用户升级VIP等级，自动为其开通对应等级特权',
            '活动奖励特权发放，已为用户开通限时特权',
            '用户投诉特权未生效，经核查后重新激活特权',
            '系统维护后特权状态异常，已批量修复用户特权',
            '用户申请特权转移，已完成角色间特权迁移',
            '补偿用户因系统故障导致的特权损失',
            '用户参与活动获得特权奖励，已发放到账'
        ]

        # 创建最近30天的处理记录
        for i in range(50):  # 创建50条记录
            # 随机选择特权
            privilege = random.choice(privileges)

            # 随机生成角色信息
            character_name = random.choice(character_names)
            character_id = 100000 + i
            vip_level = random.randint(0, 10)

            # 随机选择处理人和处理详情
            processor = random.choice(processors)
            process_detail = random.choice(process_details)

            # 随机生成处理时间（最近30天内）
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)

            processed_at = timezone.now() - timedelta(
                days=days_ago,
                hours=hours_ago,
                minutes=minutes_ago
            )

            # 创建处理记录
            record = PrivilegeProcessRecord.objects.create(
                character_name=character_name,
                character_id=character_id,
                vip_level=vip_level,
                privilege=privilege,
                process_detail=process_detail,
                processor=processor,
                processed_at=processed_at
            )

            self.stdout.write(f'创建处理记录: {record.character_name}({record.character_id}) - {record.privilege.name}')


