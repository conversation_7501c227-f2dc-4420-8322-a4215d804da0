# Generated by Django 5.2.4 on 2025-08-05 08:46

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('privileges', '0002_remove_privilege_parameters'),
        ('service_analysis', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PrivilegeProcessRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('character_name', models.CharField(max_length=100, verbose_name='角色名')),
                ('character_id', models.IntegerField(verbose_name='角色ID')),
                ('vip_level', models.IntegerField(default=0, verbose_name='VIP等级')),
                ('process_detail', models.TextField(verbose_name='处理详情')),
                ('processor', models.CharField(max_length=100, verbose_name='处理人')),
                ('processed_at', models.DateTimeField(auto_now_add=True, verbose_name='处理时间')),
                ('privilege', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='privileges.privilege', verbose_name='特权类型')),
            ],
            options={
                'verbose_name': '特权处理记录',
                'verbose_name_plural': '特权处理记录',
                'ordering': ['-processed_at'],
            },
        ),
        migrations.DeleteModel(
            name='ServiceStats',
        ),
        migrations.RemoveField(
            model_name='serviceticket',
            name='category',
        ),
        migrations.RemoveField(
            model_name='ticketresponse',
            name='ticket',
        ),
        migrations.DeleteModel(
            name='TicketCategory',
        ),
        migrations.DeleteModel(
            name='ServiceTicket',
        ),
        migrations.DeleteModel(
            name='TicketResponse',
        ),
        migrations.AddIndex(
            model_name='privilegeprocessrecord',
            index=models.Index(fields=['character_id'], name='service_ana_charact_f4aa54_idx'),
        ),
        migrations.AddIndex(
            model_name='privilegeprocessrecord',
            index=models.Index(fields=['vip_level'], name='service_ana_vip_lev_2b13ae_idx'),
        ),
        migrations.AddIndex(
            model_name='privilegeprocessrecord',
            index=models.Index(fields=['privilege'], name='service_ana_privile_8a8721_idx'),
        ),
        migrations.AddIndex(
            model_name='privilegeprocessrecord',
            index=models.Index(fields=['processed_at'], name='service_ana_process_928a0a_idx'),
        ),
        migrations.AddIndex(
            model_name='privilegeprocessrecord',
            index=models.Index(fields=['processor'], name='service_ana_process_0e1cf7_idx'),
        ),
    ]
