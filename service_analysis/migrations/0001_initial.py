# Generated by Django 5.2.4 on 2025-08-05 08:30

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TicketCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('bug_report', 'BUG反馈处理'), ('privilege_request', '特权申请审核'), ('error_recovery', '误操作恢复'), ('payment_issue', '充值异常处理'), ('account_security', '账号安全咨询'), ('other_inquiry', '其他咨询服务')], max_length=20, unique=True, verbose_name='分类名称')),
                ('display_name', models.CharField(max_length=50, verbose_name='显示名称')),
                ('icon', models.Char<PERSON>ield(max_length=50, verbose_name='图标类名')),
                ('color', models.CharField(default='#3b82f6', max_length=7, verbose_name='主题色')),
                ('description', models.TextField(blank=True, verbose_name='分类描述')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '工单分类',
                'verbose_name_plural': '工单分类',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ServiceStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='统计日期')),
                ('staff_name', models.CharField(max_length=100, verbose_name='客服姓名')),
                ('tickets_created', models.IntegerField(default=0, verbose_name='新建工单数')),
                ('tickets_resolved', models.IntegerField(default=0, verbose_name='解决工单数')),
                ('tickets_closed', models.IntegerField(default=0, verbose_name='关闭工单数')),
                ('avg_response_time', models.FloatField(default=0.0, verbose_name='平均响应时间(小时)')),
                ('avg_resolution_time', models.FloatField(default=0.0, verbose_name='平均解决时间(小时)')),
                ('avg_satisfaction', models.FloatField(default=0.0, verbose_name='平均满意度')),
                ('total_ratings', models.IntegerField(default=0, verbose_name='评价总数')),
            ],
            options={
                'verbose_name': '客服统计',
                'verbose_name_plural': '客服统计',
                'ordering': ['-date', 'staff_name'],
                'unique_together': {('date', 'staff_name')},
            },
        ),
        migrations.CreateModel(
            name='ServiceTicket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ticket_id', models.CharField(max_length=20, unique=True, verbose_name='工单号')),
                ('title', models.CharField(max_length=200, verbose_name='工单标题')),
                ('description', models.TextField(verbose_name='问题描述')),
                ('user_id', models.IntegerField(verbose_name='用户ID')),
                ('user_name', models.CharField(max_length=100, verbose_name='用户名')),
                ('user_vip_level', models.IntegerField(default=0, verbose_name='用户VIP等级')),
                ('status', models.CharField(choices=[('pending', '待处理'), ('processing', '处理中'), ('waiting_user', '等待用户'), ('resolved', '已解决'), ('closed', '已关闭'), ('rejected', '已拒绝')], default='pending', max_length=20, verbose_name='处理状态')),
                ('priority', models.CharField(choices=[('low', '低'), ('normal', '普通'), ('high', '高'), ('urgent', '紧急')], default='normal', max_length=10, verbose_name='优先级')),
                ('assigned_to', models.CharField(blank=True, max_length=100, verbose_name='分配给')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('first_response_at', models.DateTimeField(blank=True, null=True, verbose_name='首次响应时间')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='解决时间')),
                ('closed_at', models.DateTimeField(blank=True, null=True, verbose_name='关闭时间')),
                ('response_time_minutes', models.IntegerField(blank=True, null=True, verbose_name='响应时间(分钟)')),
                ('resolution_time_minutes', models.IntegerField(blank=True, null=True, verbose_name='解决时间(分钟)')),
                ('satisfaction_score', models.IntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='满意度评分')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='service_analysis.ticketcategory', verbose_name='工单分类')),
            ],
            options={
                'verbose_name': '客服工单',
                'verbose_name_plural': '客服工单',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='TicketResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('responder_type', models.CharField(choices=[('user', '用户'), ('staff', '客服')], max_length=10, verbose_name='回复者类型')),
                ('responder_name', models.CharField(max_length=100, verbose_name='回复者')),
                ('content', models.TextField(verbose_name='回复内容')),
                ('is_internal', models.BooleanField(default=False, verbose_name='内部备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='回复时间')),
                ('ticket', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='service_analysis.serviceticket', verbose_name='工单')),
            ],
            options={
                'verbose_name': '工单回复',
                'verbose_name_plural': '工单回复',
                'ordering': ['created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='serviceticket',
            index=models.Index(fields=['status'], name='service_ana_status_b2ad84_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceticket',
            index=models.Index(fields=['category'], name='service_ana_categor_e321b3_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceticket',
            index=models.Index(fields=['user_vip_level'], name='service_ana_user_vi_df1337_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceticket',
            index=models.Index(fields=['created_at'], name='service_ana_created_66cd8b_idx'),
        ),
        migrations.AddIndex(
            model_name='serviceticket',
            index=models.Index(fields=['assigned_to'], name='service_ana_assigne_44b2ea_idx'),
        ),
    ]
