from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from datetime import datetime, timedelta


def user_lifecycle(request):
    """用户生命周期管理页面视图"""
    context = {
        'page_title': '用户生命周期管理',
        'page_description': '个体用户的深度管理和生命周期追踪',
    }
    return render(request, 'user_lifecycle/lifecycle.html', context)


def user_detail(request, user_id):
    """用户详情页面视图"""
    # 模拟用户数据（实际项目中应该从数据库获取）
    user_data = get_mock_user_data(user_id)

    context = {
        'user': user_data,
    }
    return render(request, 'user_lifecycle/user_detail.html', context)


def get_mock_user_data(user_id):
    """获取模拟用户数据"""
    # 根据用户ID生成不同的模拟数据
    users_data = {
        100001: {
            'id': 100001,
            'name': '龙傲天',
            'status': 'active',
            'vip_level': 10,
            'total_recharge': 128500,
            'last_login_display': '2小时前',
            'lifecycle_days': 365,
            'register_date': '2023-08-01',
            'lifecycle_stage': 'mature',
            'lifecycle_stage_display': '成熟期',
            'activity_score': 75,
            'game_level': 85,
            'combat_power': 1250000,
            'guild_name': '龙腾天下',
            'server_name': '服务器001',
            'monthly_recharge': 8500,
            'monthly_login_days': 28,
            'avg_online_hours': 6.5,
            'social_activity_score': 92,
            'tags': [
                {'name': '高价值', 'type': 'high-value'},
                {'name': '稳定', 'type': 'stable'},
                {'name': '社交活跃', 'type': 'social'},
            ],
            'recent_recharges': [
                {'amount': 648, 'product_name': '至尊礼包', 'created_at': '2024-08-05 14:30'},
                {'amount': 328, 'product_name': '月卡续费', 'created_at': '2024-08-03 09:15'},
                {'amount': 98, 'product_name': '体力补充包', 'created_at': '2024-08-01 20:45'},
            ],
            'recent_logins': [
                {'login_time': '2024-08-06 10:30', 'duration': 4.5, 'device_type': 'Android'},
                {'login_time': '2024-08-05 19:15', 'duration': 3.2, 'device_type': 'iOS'},
                {'login_time': '2024-08-05 08:45', 'duration': 2.8, 'device_type': 'Android'},
            ],
        },
        100002: {
            'id': 100002,
            'name': '剑神无双',
            'status': 'risk',
            'vip_level': 8,
            'total_recharge': 45200,
            'last_login_display': '7天前',
            'lifecycle_days': 180,
            'register_date': '2024-02-01',
            'lifecycle_stage': 'decline',
            'lifecycle_stage_display': '衰退期',
            'activity_score': 25,
            'game_level': 72,
            'combat_power': 850000,
            'guild_name': '剑客联盟',
            'server_name': '服务器002',
            'monthly_recharge': 0,
            'monthly_login_days': 5,
            'avg_online_hours': 1.2,
            'social_activity_score': 35,
            'tags': [
                {'name': '流失风险', 'type': 'risk'},
                {'name': '活跃下降', 'type': 'declining'},
            ],
            'recent_recharges': [
                {'amount': 198, 'product_name': '战力提升包', 'created_at': '2024-07-15 16:20'},
            ],
            'recent_logins': [
                {'login_time': '2024-07-30 15:20', 'duration': 1.5, 'device_type': 'Android'},
                {'login_time': '2024-07-28 11:30', 'duration': 0.8, 'device_type': 'Android'},
            ],
        },
        100003: {
            'id': 100003,
            'name': '花间一壶酒',
            'status': 'active',
            'vip_level': 6,
            'total_recharge': 18900,
            'last_login_display': '30分钟前',
            'lifecycle_days': 90,
            'register_date': '2024-05-01',
            'lifecycle_stage': 'growth',
            'lifecycle_stage_display': '成长期',
            'activity_score': 60,
            'game_level': 58,
            'combat_power': 420000,
            'guild_name': '诗酒年华',
            'server_name': '服务器003',
            'monthly_recharge': 1200,
            'monthly_login_days': 25,
            'avg_online_hours': 4.2,
            'social_activity_score': 78,
            'tags': [
                {'name': '潜力用户', 'type': 'potential'},
                {'name': '快速成长', 'type': 'growing'},
            ],
            'recent_recharges': [
                {'amount': 328, 'product_name': '成长礼包', 'created_at': '2024-08-04 12:15'},
                {'amount': 98, 'product_name': '每日特惠', 'created_at': '2024-08-02 18:30'},
            ],
            'recent_logins': [
                {'login_time': '2024-08-06 11:30', 'duration': 3.5, 'device_type': 'iOS'},
                {'login_time': '2024-08-05 20:15', 'duration': 4.2, 'device_type': 'iOS'},
                {'login_time': '2024-08-05 14:45', 'duration': 2.1, 'device_type': 'iOS'},
            ],
        },
        100004: {
            'id': 100004,
            'name': '独孤求败',
            'status': 'churned',
            'vip_level': 5,
            'total_recharge': 12300,
            'last_login_display': '30天前',
            'lifecycle_days': 120,
            'register_date': '2024-03-15',
            'lifecycle_stage': 'decline',
            'lifecycle_stage_display': '已流失',
            'activity_score': 0,
            'game_level': 45,
            'combat_power': 280000,
            'guild_name': '无',
            'server_name': '服务器001',
            'monthly_recharge': 0,
            'monthly_login_days': 0,
            'avg_online_hours': 0,
            'social_activity_score': 15,
            'tags': [
                {'name': '已流失', 'type': 'risk'},
                {'name': '需要挽回', 'type': 'declining'},
            ],
            'recent_recharges': [
                {'amount': 98, 'product_name': '基础礼包', 'created_at': '2024-06-20 10:15'},
            ],
            'recent_logins': [
                {'login_time': '2024-07-06 16:30', 'duration': 0.5, 'device_type': 'Android'},
            ],
        },
    }

    # 如果用户ID不存在，返回默认数据
    return users_data.get(user_id, users_data[100001])
