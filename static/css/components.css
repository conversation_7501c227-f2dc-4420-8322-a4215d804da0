/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: 6px 12px;
    font-size: 0.8125rem;
    font-weight: 500;
    line-height: 1;
    border: 1px solid transparent;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary);
    color: var(--primary-foreground);
    border-color: var(--primary);
}

.btn-primary:hover:not(:disabled) {
    background-color: hsl(221.2, 83.2%, 48%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--secondary);
    color: var(--secondary-foreground);
    border-color: var(--border);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--muted);
    transform: translateY(-1px);
}

.btn-outline {
    background-color: transparent;
    color: var(--foreground);
    border-color: var(--border);
}

.btn-outline:hover:not(:disabled) {
    background-color: var(--muted);
    transform: translateY(-1px);
}

.btn-success {
    background-color: var(--success);
    color: white;
    border-color: var(--success);
}

.btn-success:hover:not(:disabled) {
    background-color: #059669;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background-color: var(--warning);
    color: white;
    border-color: var(--warning);
}

.btn-warning:hover:not(:disabled) {
    background-color: #d97706;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-error {
    background-color: var(--error);
    color: white;
    border-color: var(--error);
}

.btn-error:hover:not(:disabled) {
    background-color: #dc2626;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 8px 16px;
    font-size: 0.875rem;
}

/* 卡片组件 */
.card {
    background-color: var(--card);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border);
    background-color: var(--muted);
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--card-foreground);
    margin: 0;
}

.card-description {
    font-size: 0.8125rem;
    color: var(--muted-foreground);
    margin: 2px 0 0 0;
}

.card-content {
    padding: var(--spacing-md);
}

.card-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border);
    background-color: var(--muted);
}

/* 输入框组件 */
.input {
    width: 100%;
    padding: 10px 12px;
    font-size: 0.875rem;
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    background-color: var(--background);
    color: var(--foreground);
    transition: var(--transition-fast);
}

.input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px hsla(221.2, 83.2%, 53.3%, 0.1);
}

.input::placeholder {
    color: var(--muted-foreground);
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group .input {
    padding-left: 40px;
}

.input-icon {
    position: absolute;
    left: 12px;
    color: var(--muted-foreground);
    pointer-events: none;
}

/* 标签组件 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 12px;
    white-space: nowrap;
}

.badge-primary {
    background-color: hsla(221.2, 83.2%, 53.3%, 0.1);
    color: var(--primary);
}

.badge-success {
    background-color: hsla(142, 71%, 45%, 0.1);
    color: var(--success);
}

.badge-warning {
    background-color: hsla(38, 92%, 50%, 0.1);
    color: var(--warning);
}

.badge-error {
    background-color: hsla(0, 84%, 60%, 0.1);
    color: var(--error);
}

.badge-secondary {
    background-color: var(--muted);
    color: var(--muted-foreground);
}

/* 统计卡片 */
.stat-card {
    background-color: var(--card);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), #8b5cf6);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-card.stat-success::before {
    background: var(--success);
}

.stat-card.stat-warning::before {
    background: var(--warning);
}

.stat-card.stat-error::before {
    background: var(--error);
}

.stat-card.stat-info::before {
    background: var(--info);
}

.stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.stat-title {
    font-size: 0.8125rem;
    color: var(--muted-foreground);
    font-weight: 500;
}

.stat-icon {
    width: 32px;
    height: 32px;
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    background: linear-gradient(135deg, var(--primary), #8b5cf6);
    color: white;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--foreground);
    margin-bottom: 2px;
}

.stat-change {
    font-size: 0.6875rem;
    display: flex;
    align-items: center;
    gap: 3px;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--error);
}

/* 工具栏 */
.toolbar {
    background-color: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.toolbar-search {
    flex: 1;
    min-width: 300px;
}

.toolbar-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* 特权卡片网格 */
.privileges-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-md);
}

.privilege-card {
    background-color: var(--card);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.privilege-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 4px;
    background-color: var(--success);
    transition: var(--transition);
}

.privilege-card.disabled {
    opacity: 0.7;
}

.privilege-card.disabled::before {
    background-color: var(--error);
}

.privilege-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.privilege-header {
    padding: var(--spacing-md);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
}

.privilege-info {
    flex: 1;
}

.privilege-icon {
    width: 36px;
    height: 36px;
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(135deg, var(--success), #10b981);
    color: white;
}

.privilege-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 2px;
}

.privilege-type {
    font-size: 0.8125rem;
    color: var(--muted-foreground);
}

.privilege-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-active .status-dot {
    background-color: var(--success);
}

.status-disabled .status-dot {
    background-color: var(--error);
}

.privilege-description {
    padding: 0 var(--spacing-md) var(--spacing-sm);
    color: var(--muted-foreground);
    font-size: 0.8125rem;
    line-height: 1.4;
}

.privilege-footer {
    padding: var(--spacing-sm) var(--spacing-md);
    border-top: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.privilege-usage {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8125rem;
    color: var(--muted-foreground);
}

.privilege-actions {
    display: flex;
    gap: 4px;
}

/* 页面头部 */
.page-header {
    background-color: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), #8b5cf6, #ec4899);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(135deg, var(--primary), #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-description {
    font-size: 0.875rem;
    color: var(--muted-foreground);
    margin: 0;
}
