/* 基础样式和CSS变量系统 */
:root {
    /* 主色调 */
    --primary: hsl(221.2, 83.2%, 53.3%);
    --primary-foreground: hsl(210, 40%, 98%);
    
    /* 辅助色 */
    --secondary: hsl(210, 40%, 96%);
    --secondary-foreground: hsl(222.2, 84%, 4.9%);
    
    /* 语义色 */
    --success: #10B981;
    --warning: #F59E0B;
    --error: #EF4444;
    --info: #3B82F6;
    
    /* 灰色系 */
    --background: hsl(0, 0%, 100%);
    --foreground: hsl(222.2, 84%, 4.9%);
    --muted: hsl(210, 40%, 96%);
    --muted-foreground: hsl(215.4, 16.3%, 46.9%);
    --border: hsl(214.3, 31.8%, 91.4%);
    --input: hsl(214.3, 31.8%, 91.4%);
    --ring: hsl(221.2, 83.2%, 53.3%);
    
    /* 卡片和表面 */
    --card: hsl(0, 0%, 100%);
    --card-foreground: hsl(222.2, 84%, 4.9%);
    --surface: hsl(0, 0%, 100%);
    
    /* 间距系统 - 紧凑版 */
    --spacing-xs: 2px;
    --spacing-sm: 4px;
    --spacing-md: 8px;
    --spacing-lg: 12px;
    --spacing-xl: 16px;
    --spacing-2xl: 24px;
    
    /* 圆角系统 */
    --radius-sm: 4px;
    --radius: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* 过渡动画 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 暗色主题 */
.dark,
@media (prefers-color-scheme: dark) {
    :root {
        --background: hsl(222.2, 84%, 4.9%);
        --foreground: hsl(210, 40%, 98%);
        --muted: hsl(217.2, 32.6%, 17.5%);
        --muted-foreground: hsl(215, 20.2%, 65.1%);
        --border: hsl(217.2, 32.6%, 17.5%);
        --input: hsl(217.2, 32.6%, 17.5%);
        --card: hsl(222.2, 84%, 4.9%);
        --card-foreground: hsl(210, 40%, 98%);
        --surface: hsl(217.2, 32.6%, 17.5%);
        --primary-foreground: hsl(222.2, 84%, 4.9%);
        --secondary: hsl(217.2, 32.6%, 17.5%);
        --secondary-foreground: hsl(210, 40%, 98%);
    }
}

/* 基础重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.6;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    background-color: var(--background);
    color: var(--foreground);
    transition: var(--transition);
}

/* 通用工具类 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-1 {
    gap: var(--spacing-xs);
}

.gap-2 {
    gap: var(--spacing-sm);
}

.gap-3 {
    gap: 6px;
}

.gap-4 {
    gap: var(--spacing-md);
}

.gap-6 {
    gap: var(--spacing-lg);
}

.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.hidden {
    display: none;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 紧凑模式工具类 */
.compact {
    padding: var(--spacing-sm) !important;
}

.compact-x {
    padding-left: var(--spacing-sm) !important;
    padding-right: var(--spacing-sm) !important;
}

.compact-y {
    padding-top: var(--spacing-sm) !important;
    padding-bottom: var(--spacing-sm) !important;
}

.text-compact {
    font-size: 0.8125rem;
    line-height: 1.3;
}

/* 响应式断点 */
@media (min-width: 640px) {
    .sm\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 768px) {
    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}
