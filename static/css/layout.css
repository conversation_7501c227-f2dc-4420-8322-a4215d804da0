/* 应用布局 */
.app-layout {
    display: flex;
    min-height: 100vh;
    background-color: var(--background);
}

/* 侧边栏样式 */
.sidebar {
    width: 220px;
    background-color: var(--surface);
    border-right: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    position: relative;
    z-index: 10;
}

.sidebar.collapsed {
    width: 56px;
}

.sidebar-header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    flex: 1;
}

.brand-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: 1px;
    background: linear-gradient(135deg, var(--primary), #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-subtitle {
    font-size: 0.6875rem;
    color: var(--muted-foreground);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--muted-foreground);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius);
    transition: var(--transition-fast);
    display: none;
}

.sidebar-toggle:hover {
    background-color: var(--muted);
    color: var(--foreground);
}

/* 导航样式 */
.sidebar-nav {
    flex: 1;
    padding: var(--spacing-sm) 0;
    overflow-y: auto;
}

.nav-list {
    list-style: none;
    padding: 0 var(--spacing-sm);
}

.nav-item {
    margin-bottom: 1px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 8px var(--spacing-md);
    color: var(--muted-foreground);
    text-decoration: none;
    border-radius: var(--radius);
    transition: var(--transition-fast);
    position: relative;
    gap: 10px;
    font-size: 0.875rem;
}

.nav-link:hover {
    background-color: var(--muted);
    color: var(--foreground);
    transform: translateX(2px);
}

.nav-link.active {
    background-color: var(--primary);
    color: var(--primary-foreground);
    box-shadow: var(--shadow-md);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: -var(--spacing-md);
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: var(--primary-foreground);
    border-radius: 0 2px 2px 0;
}

.nav-link i {
    font-size: 1rem;
    width: 18px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    font-weight: 500;
    white-space: nowrap;
}

.nav-badge {
    background-color: var(--error);
    color: white;
    font-size: 0.75rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
    min-width: 18px;
    text-align: center;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: var(--spacing-sm) var(--spacing-md);
    border-top: 1px solid var(--border);
}

.system-info {
    text-align: center;
}

.version {
    font-size: 0.75rem;
    color: var(--muted-foreground);
    margin-bottom: 4px;
}

.status {
    font-size: 0.75rem;
    color: var(--muted-foreground);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
}

.status-online {
    background-color: var(--success);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
}

/* 顶部导航栏 */
.top-header {
    height: 56px;
    background-color: var(--surface);
    border-bottom: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 5;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.mobile-menu-toggle {
    background: none;
    border: none;
    color: var(--muted-foreground);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius);
    transition: var(--transition-fast);
    display: none;
}

.mobile-menu-toggle:hover {
    background-color: var(--muted);
    color: var(--foreground);
}

.breadcrumb {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

.breadcrumb-item {
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.header-btn {
    background: none;
    border: none;
    color: var(--muted-foreground);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius);
    transition: var(--transition-fast);
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-btn:hover {
    background-color: var(--muted);
    color: var(--foreground);
}

.notification-badge {
    position: absolute;
    top: 6px;
    right: 6px;
    background-color: var(--error);
    color: white;
    font-size: 0.625rem;
    padding: 1px 4px;
    border-radius: 6px;
    min-width: 14px;
    text-align: center;
}

/* 用户菜单 */
.user-menu {
    position: relative;
}

.user-avatar {
    background: none;
    border: none;
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.user-avatar:hover {
    box-shadow: 0 0 0 2px var(--primary);
}

.user-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background-color: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: var(--transition-fast);
    z-index: 50;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: 12px var(--spacing-md);
    color: var(--foreground);
    text-decoration: none;
    transition: var(--transition-fast);
}

.dropdown-item:hover {
    background-color: var(--muted);
}

.dropdown-divider {
    margin: 4px 0;
    border: none;
    border-top: 1px solid var(--border);
}

/* 页面内容 */
.page-content {
    flex: 1;
    padding: var(--spacing-md);
    overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .sidebar {
        position: fixed;
        left: -220px;
        top: 0;
        bottom: 0;
        z-index: 20;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        width: 100%;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .sidebar-toggle {
        display: flex;
    }
}

@media (max-width: 768px) {
    .page-content {
        padding: var(--spacing-sm);
    }

    .top-header {
        padding: 0 var(--spacing-sm);
        height: 48px;
    }

    .sidebar {
        width: 200px;
    }

    .sidebar.collapsed {
        width: 48px;
    }
}
