# MMO游戏大R用户维护系统

一个专门用于管理游戏中高价值用户（大R用户）的管理平台。

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Django 5.2+

### 快速安装（推荐）

1. 克隆项目
```bash
git clone <repository-url>
cd VIPManager
```

2. 创建虚拟环境并安装依赖
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows

pip install django
```

3. 运行快速设置脚本
```bash
python setup.py
```

4. 启动开发服务器
```bash
python manage.py runserver
```

5. 访问应用
- 特权管理页面: `http://127.0.0.1:8000`
- 管理后台: `http://127.0.0.1:8000/admin`

### 手动安装

如果快速设置脚本失败，可以手动执行以下步骤：

```bash
# 1. 创建数据库迁移
python manage.py makemigrations

# 2. 执行数据库迁移
python manage.py migrate

# 3. 初始化示例数据
python manage.py init_privilege_data

# 4. 创建管理员账户（可选）
python manage.py createsuperuser

# 5. 启动服务器
python manage.py runserver
```

## 📋 功能模块

### ✅ 已完成
- **特权管理页面** - VIP特权项目的配置和管理中心
  - 统一的侧边栏导航
  - 响应式设计
  - 实时搜索功能
  - 特权状态管理
  - 统计数据展示
  - 后端数据模型和API
  - 数据库集成
  - 示例数据初始化
  - **CRUD功能** - 完整的增删改查功能
    - ✅ 添加特权 - 通过模态框添加新特权
    - ✅ 编辑特权 - 编辑现有特权信息
    - ✅ 删除特权 - 删除特权（带确认提示）
    - ✅ 表单验证 - 完整的前后端验证
    - ✅ 错误处理 - 友好的错误提示

### 🔄 开发中
- 大R用户总览页面
- 流失预警中心
- 潜力挖掘分析页面
- 用户生命周期管理页面
- 特权处理分析页面
- 系统配置页面

## 🎨 设计特色

- **现代化界面**: 基于现代设计系统的清爽界面
- **响应式布局**: 支持桌面端、平板端、移动端
- **主题切换**: 支持明暗主题自动切换
- **组件化设计**: 统一的UI组件库
- **流畅动画**: 丰富的交互动画效果

## 🛠️ 技术栈

- **后端**: Django 5.2 + SQLite
- **前端**: HTML5 + CSS3 + JavaScript
- **图标**: Bootstrap Icons
- **设计**: CSS变量 + Flexbox/Grid

## 📁 项目结构

```
VIPManager/
├── VIPManager/          # Django项目配置
├── privileges/          # 特权管理应用
│   ├── models.py       # 数据模型
│   ├── views.py        # 视图和API
│   ├── admin.py        # 管理后台
│   ├── management/     # 管理命令
│   └── migrations/     # 数据库迁移
├── templates/           # HTML模板
├── static/             # 静态文件
│   ├── css/           # 样式文件
│   └── js/            # JavaScript文件
├── 功能分析/           # 功能分析文档
├── docs/              # 项目文档
└── setup.py           # 快速设置脚本
```

## 📖 开发指南

### 页面开发流程
1. 先开发HTML页面，确保界面设计和用户体验
2. 逐页面推进，一个页面一个功能点进行开发
3. 保持整体界面风格的一致性
4. 页面确认后再开发模型和API

### 设计规范
- 使用统一的CSS变量系统
- 遵循16px基础间距系统
- 保持组件设计的一致性
- 支持响应式设计

## 🎯 下一步计划

1. 开发大R用户总览页面
2. 实现流失预警中心
3. 添加数据可视化图表
4. 集成实时数据更新
5. 完善用户权限管理

## 🔧 API接口

### 特权管理API
- `GET /privileges/api/list/` - 获取特权列表
- `GET /privileges/api/stats/` - 获取统计数据
- `GET /privileges/api/{id}/` - 获取特权详情
- `POST /privileges/api/{id}/toggle/` - 切换特权状态

### 数据模型
- **VIPLevel** - VIP等级管理
- **PrivilegeCategory** - 特权分类
- **Privilege** - 特权主体
- **PrivilegeUsageLog** - 使用记录
- **PrivilegeStats** - 统计数据

## 📝 更新日志

### v1.2.0 (2025-08-05)
- ✅ 完成特权CRUD功能开发
- ✅ 实现模态框组件系统
- ✅ 添加表单验证和错误处理
- ✅ 完善API接口和数据模型
- ✅ 优化用户交互体验

### v1.1.0 (2025-08-05)
- ✅ 完成特权管理后端开发
- ✅ 实现数据模型和API接口
- ✅ 集成真实数据库数据
- ✅ 添加管理后台支持
- ✅ 创建示例数据初始化

### v1.0.0 (2025-08-05)
- ✅ 完成特权管理页面HTML开发
- ✅ 实现基础布局和导航系统
- ✅ 添加响应式设计支持
- ✅ 实现主题切换功能
- ✅ 完成搜索和交互功能

## 📄 许可证

本项目仅供学习和开发使用。
