# 大R用户总览页面 - 数据库设计与计算方式

## 📋 概述

本文档详细描述了大R用户总览页面所需的数据库设计、数据模型以及各项指标的计算方式。该页面作为系统主仪表盘，需要提供准确、实时的大R用户数据分析。

## 🗄️ 数据库设计

### 1. 核心数据模型

#### 1.1 BigRUser（大R用户模型）
```python
class BigRUser(models.Model):
    """大R用户基础信息模型"""

    # 基础信息
    user_id = models.BigIntegerField(unique=True, verbose_name="用户ID")
    username = models.CharField(max_length=100, verbose_name="用户名")
    character_name = models.CharField(max_length=100, verbose_name="角色名")
    server_id = models.IntegerField(verbose_name="服务器ID")
    server_name = models.CharField(max_length=50, verbose_name="服务器名称")

    # 充值信息
    total_recharge = models.DecimalField(max_digits=12, decimal_places=2, default=0, verbose_name="累计充值金额")
    first_recharge_date = models.DateTimeField(null=True, blank=True, verbose_name="首次充值时间")
    last_recharge_date = models.DateTimeField(null=True, blank=True, verbose_name="最后充值时间")

    # 活跃度信息
    last_login_date = models.DateTimeField(null=True, blank=True, verbose_name="最后登录时间")
    total_login_days = models.IntegerField(default=0, verbose_name="累计登录天数")
    consecutive_login_days = models.IntegerField(default=0, verbose_name="连续登录天数")

    # 状态标识
    is_potential = models.BooleanField(default=False, verbose_name="是否为潜力用户")
    is_churn_risk = models.BooleanField(default=False, verbose_name="是否有流失风险")
    churn_risk_level = models.CharField(
        max_length=10,
        choices=[('low', '低风险'), ('medium', '中风险'), ('high', '高风险'), ('critical', '严重风险')],
        default='low',
        verbose_name="流失风险等级"
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "大R用户"
        verbose_name_plural = "大R用户"
        indexes = [
            models.Index(fields=['user_id']),
            models.Index(fields=['total_recharge']),
            models.Index(fields=['last_login_date']),
            models.Index(fields=['is_potential']),
            models.Index(fields=['is_churn_risk']),
        ]

    @property
    def vip_level(self):
        """动态计算VIP等级"""
        # VIP等级根据累计充值金额即时计算
        if self.total_recharge >= 100000:
            return 10
        elif self.total_recharge >= 50000:
            return 9
        elif self.total_recharge >= 20000:
            return 8
        elif self.total_recharge >= 10000:
            return 7
        elif self.total_recharge >= 5000:
            return 6
        elif self.total_recharge >= 2000:
            return 5
        elif self.total_recharge >= 1000:
            return 4
        elif self.total_recharge >= 500:
            return 3
        elif self.total_recharge >= 200:
            return 2
        elif self.total_recharge >= 100:
            return 1
        else:
            return 0
```

#### 1.2 UserRechargeRecord（用户充值记录模型）
```python
class UserRechargeRecord(models.Model):
    """用户充值记录模型"""
    
    user = models.ForeignKey(BigRUser, on_delete=models.CASCADE, verbose_name="用户")
    order_id = models.CharField(max_length=100, unique=True, verbose_name="订单号")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="充值金额")
    currency = models.CharField(max_length=10, default='CNY', verbose_name="货币类型")
    payment_method = models.CharField(max_length=50, verbose_name="支付方式")
    
    # 充值状态
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '待支付'),
            ('success', '支付成功'),
            ('failed', '支付失败'),
            ('refunded', '已退款')
        ],
        default='pending',
        verbose_name="支付状态"
    )
    
    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    paid_at = models.DateTimeField(null=True, blank=True, verbose_name="支付时间")
    
    class Meta:
        verbose_name = "用户充值记录"
        verbose_name_plural = "用户充值记录"
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['status']),
            models.Index(fields=['paid_at']),
        ]
```

#### 1.3 UserLoginRecord（用户登录记录模型）
```python
class UserLoginRecord(models.Model):
    """用户登录记录模型"""
    
    user = models.ForeignKey(BigRUser, on_delete=models.CASCADE, verbose_name="用户")
    login_date = models.DateField(verbose_name="登录日期")
    login_time = models.DateTimeField(verbose_name="登录时间")
    online_duration = models.IntegerField(default=0, verbose_name="在线时长(分钟)")
    ip_address = models.GenericIPAddressField(null=True, blank=True, verbose_name="IP地址")
    device_type = models.CharField(max_length=50, null=True, blank=True, verbose_name="设备类型")
    
    class Meta:
        verbose_name = "用户登录记录"
        verbose_name_plural = "用户登录记录"
        unique_together = ['user', 'login_date']  # 每天只记录一次登录
        indexes = [
            models.Index(fields=['user', 'login_date']),
            models.Index(fields=['login_date']),
        ]
```

#### 1.4 BigROverviewStats（大R总览统计模型）
```python
class BigROverviewStats(models.Model):
    """大R总览统计数据模型"""
    
    # 统计日期
    stat_date = models.DateField(unique=True, verbose_name="统计日期")
    
    # 核心指标
    total_big_r_users = models.IntegerField(default=0, verbose_name="大R用户总数")
    total_potential_users = models.IntegerField(default=0, verbose_name="潜力用户数量")
    total_churn_warning_users = models.IntegerField(default=0, verbose_name="流失预警用户数")
    
    # ARPU相关
    total_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=0, verbose_name="总收入")
    avg_arpu = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="平均ARPU值")
    
    # VIP分布统计
    vip_distribution = models.JSONField(default=dict, verbose_name="VIP等级分布")
    
    # 增长数据
    new_big_r_users = models.IntegerField(default=0, verbose_name="新增大R用户")
    lost_big_r_users = models.IntegerField(default=0, verbose_name="流失大R用户")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "大R总览统计"
        verbose_name_plural = "大R总览统计"
        ordering = ['-stat_date']
```

### 2. 辅助数据模型

#### 2.1 UserBehaviorAnalysis（用户行为分析模型）
```python
class UserBehaviorAnalysis(models.Model):
    """用户行为分析模型"""
    
    user = models.OneToOneField(BigRUser, on_delete=models.CASCADE, verbose_name="用户")
    
    # 充值行为分析
    avg_recharge_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="平均充值金额")
    recharge_frequency = models.FloatField(default=0, verbose_name="充值频率(次/月)")
    days_since_last_recharge = models.IntegerField(default=0, verbose_name="距离上次充值天数")
    
    # 活跃度分析
    avg_online_duration = models.IntegerField(default=0, verbose_name="平均在线时长(分钟/天)")
    login_frequency = models.FloatField(default=0, verbose_name="登录频率(天/周)")
    days_since_last_login = models.IntegerField(default=0, verbose_name="距离上次登录天数")
    
    # 潜力评分
    potential_score = models.FloatField(default=0, verbose_name="潜力评分(0-100)")
    churn_risk_score = models.FloatField(default=0, verbose_name="流失风险评分(0-100)")
    
    # 更新时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        verbose_name = "用户行为分析"
        verbose_name_plural = "用户行为分析"
```

## 📊 数据计算方式

### 1. 核心指标计算

#### 1.1 大R用户总数
```python
def calculate_total_big_r_users():
    """计算大R用户总数"""
    # 所有用户都是大R用户，返回总用户数
    return BigRUser.objects.count()

# 说明：
# 系统中的所有用户都被视为大R用户
# 这个指标显示的是系统中的总用户数量
```

#### 1.2 平均ARPU值
```python
def calculate_avg_arpu(period_days=30):
    """计算平均ARPU值"""
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=period_days)
    
    # 获取期间内的充值总额
    total_revenue = UserRechargeRecord.objects.filter(
        status='success',
        paid_at__date__range=[start_date, end_date]
    ).aggregate(total=Sum('amount'))['total'] or 0
    
    # 获取期间内活跃的用户数
    active_users = BigRUser.objects.filter(
        last_login_date__date__gte=start_date
    ).count()
    
    if active_users > 0:
        return total_revenue / active_users
    return 0
```

#### 1.3 流失预警用户数
```python
def calculate_churn_warning_users():
    """计算流失预警用户数"""
    return BigRUser.objects.filter(
        is_churn_risk=True
    ).count()

# 流失预警判定标准：
# 1. 超过7天未登录
# 2. 超过30天未充值
# 3. 流失风险评分 >= 60
```

#### 1.4 潜力用户数量
```python
def calculate_potential_users():
    """计算潜力用户数量"""
    return BigRUser.objects.filter(is_potential=True).count()

# 潜力用户判定标准：
# 1. 有一定充值记录（累计充值 >= 100元）
# 2. 活跃度较高（最近7天内有登录）
# 3. 潜力评分 >= 70
# 4. 具有进一步消费潜力的用户
```

### 2. 趋势数据计算

#### 2.1 用户增长趋势
```python
def calculate_growth_trend(period_days=30):
    """计算用户增长趋势"""
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=period_days)
    
    trend_data = []
    for i in range(period_days):
        date = start_date + timedelta(days=i)
        
        # 计算截止到该日期的累计用户数
        cumulative_users = BigRUser.objects.filter(
            created_at__date__lte=date
        ).count()
        
        trend_data.append({
            'date': date,
            'count': cumulative_users
        })
    
    return trend_data
```

#### 2.2 VIP等级分布
```python
def calculate_vip_distribution():
    """计算VIP等级分布"""
    # 使用原生SQL查询，因为vip_level是计算属性
    from django.db import connection

    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT
                CASE
                    WHEN total_recharge >= 100000 THEN 10
                    WHEN total_recharge >= 50000 THEN 9
                    WHEN total_recharge >= 20000 THEN 8
                    WHEN total_recharge >= 10000 THEN 7
                    WHEN total_recharge >= 5000 THEN 6
                    WHEN total_recharge >= 2000 THEN 5
                    WHEN total_recharge >= 1000 THEN 4
                    WHEN total_recharge >= 500 THEN 3
                    WHEN total_recharge >= 200 THEN 2
                    WHEN total_recharge >= 100 THEN 1
                    ELSE 0
                END as vip_level,
                COUNT(*) as count
            FROM big_r_overview_bigruser
            GROUP BY vip_level
            ORDER BY vip_level DESC
        """)
        distribution = cursor.fetchall()
    
    total_users = sum(item[1] for item in distribution)

    result = []
    for vip_level, count in distribution:
        percentage = (count / total_users * 100) if total_users > 0 else 0
        result.append({
            'level': f"VIP{vip_level}",
            'count': count,
            'percentage': round(percentage, 1)
        })
    
    return result
```

### 3. 用户分类算法

#### 3.1 用户数据更新
```python
def update_user_data():
    """更新用户相关数据"""
    # 所有用户都是大R用户，无需特殊标识
    # 主要更新潜力用户和流失风险标识
    identify_potential_users()
    identify_churn_risk_users()
```

#### 3.2 潜力用户识别
```python
def identify_potential_users():
    """识别潜力用户"""
    # 计算潜力评分
    for user in BigRUser.objects.all():
        score = calculate_potential_score(user)

        # 更新用户行为分析
        analysis, created = UserBehaviorAnalysis.objects.get_or_create(user=user)
        analysis.potential_score = score
        analysis.save()

        # 判定为潜力用户
        if score >= 70 and user.total_recharge >= 100:
            user.is_potential = True
            user.save()

def calculate_potential_score(user):
    """计算用户潜力评分"""
    score = 0
    
    # 充值金额权重 (40%)
    if user.total_recharge >= 500:
        score += 40
    elif user.total_recharge >= 200:
        score += 30
    elif user.total_recharge >= 100:
        score += 20
    elif user.total_recharge >= 50:
        score += 10
    
    # 活跃度权重 (30%)
    days_since_login = (timezone.now().date() - user.last_login_date.date()).days
    if days_since_login <= 1:
        score += 30
    elif days_since_login <= 3:
        score += 25
    elif days_since_login <= 7:
        score += 20
    elif days_since_login <= 14:
        score += 10
    
    # VIP等级权重 (20%)
    score += min(user.vip_level * 4, 20)
    
    # 充值频率权重 (10%)
    analysis = getattr(user, 'userbehavioranalysis', None)
    if analysis and analysis.recharge_frequency >= 2:
        score += 10
    elif analysis and analysis.recharge_frequency >= 1:
        score += 5
    
    return min(score, 100)
```

#### 3.3 流失风险识别
```python
def identify_churn_risk_users():
    """识别流失风险用户"""
    for user in BigRUser.objects.all():
        risk_score = calculate_churn_risk_score(user)
        
        # 更新用户行为分析
        analysis, created = UserBehaviorAnalysis.objects.get_or_create(user=user)
        analysis.churn_risk_score = risk_score
        analysis.save()
        
        # 判定流失风险等级
        if risk_score >= 80:
            user.churn_risk_level = 'critical'
            user.is_churn_risk = True
        elif risk_score >= 60:
            user.churn_risk_level = 'high'
            user.is_churn_risk = True
        elif risk_score >= 40:
            user.churn_risk_level = 'medium'
            user.is_churn_risk = False
        else:
            user.churn_risk_level = 'low'
            user.is_churn_risk = False
        
        user.save()

def calculate_churn_risk_score(user):
    """计算流失风险评分"""
    score = 0
    
    # 登录间隔权重 (40%)
    if user.last_login_date:
        days_since_login = (timezone.now().date() - user.last_login_date.date()).days
        if days_since_login >= 14:
            score += 40
        elif days_since_login >= 7:
            score += 30
        elif days_since_login >= 3:
            score += 20
        elif days_since_login >= 1:
            score += 10
    else:
        score += 40
    
    # 充值间隔权重 (35%)
    if user.last_recharge_date:
        days_since_recharge = (timezone.now().date() - user.last_recharge_date.date()).days
        if days_since_recharge >= 60:
            score += 35
        elif days_since_recharge >= 30:
            score += 25
        elif days_since_recharge >= 14:
            score += 15
        elif days_since_recharge >= 7:
            score += 5
    else:
        score += 35
    
    # 活跃度下降权重 (25%)
    analysis = getattr(user, 'userbehavioranalysis', None)
    if analysis:
        if analysis.login_frequency < 2:  # 每周登录少于2天
            score += 25
        elif analysis.login_frequency < 4:
            score += 15
        elif analysis.login_frequency < 6:
            score += 5
    
    return min(score, 100)
```

## 🔄 数据更新策略

### 1. 实时更新
- 用户登录时更新登录记录
- 充值成功时更新充值记录和用户信息
- 用户行为发生变化时实时更新相关字段

### 2. 定时任务
- **每日凌晨2点**：执行用户分类算法，更新大R用户、潜力用户、流失风险用户标识
- **每日凌晨3点**：生成当日的统计数据，更新BigROverviewStats表
- **每周日凌晨4点**：重新计算所有用户的行为分析数据

### 3. 缓存策略
- 核心指标数据缓存5分钟
- 趋势图表数据缓存30分钟
- VIP分布数据缓存1小时

## 📈 性能优化

### 1. 数据库索引
- 为所有查询频繁的字段添加索引
- 使用复合索引优化复杂查询
- 定期分析查询性能并优化索引

### 2. 查询优化
- 使用聚合查询减少数据库访问
- 批量更新减少数据库连接
- 使用select_related和prefetch_related优化关联查询

### 3. 数据分区
- 按时间分区存储历史数据
- 定期归档老旧数据
- 保持活跃数据表的合理大小

## 🎯 数据质量保证

### 1. 数据验证
- 充值金额必须为正数
- 日期字段不能为未来时间
- VIP等级必须在合理范围内

### 2. 数据一致性
- 使用数据库事务保证数据一致性
- 定期检查数据完整性
- 异常数据自动修复机制

### 3. 监控告警
- 关键指标异常变化告警
- 数据更新失败告警
- 性能指标监控告警

## 🔌 API接口设计

### 1. 核心指标API

#### 1.1 获取核心指标数据
```
GET /big-r-overview/api/metrics/
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "total_big_r_users": {
            "value": 1247,
            "change": "+5.2%",
            "change_type": "positive"
        },
        "avg_arpu": {
            "value": 2847.50,
            "formatted": "¥2,847",
            "change": "+12.3%",
            "change_type": "positive"
        },
        "churn_warning_users": {
            "value": 23,
            "change": "-8.1%",
            "change_type": "positive"
        },
        "potential_users": {
            "value": 892,
            "change": "+15.7%",
            "change_type": "positive"
        },
        "last_updated": "2025-08-05T17:30:00Z"
    }
}
```

#### 1.2 获取增长趋势数据
```
GET /big-r-overview/api/growth-trend/?period=30
```

**参数：**
- `period`: 时间周期（7、30、90天）

**响应示例：**
```json
{
    "success": true,
    "data": {
        "period": 30,
        "labels": ["8月1日", "8月2日", "8月3日", "..."],
        "datasets": [{
            "label": "大R用户数量",
            "data": [1200, 1205, 1210, "..."]
        }]
    }
}
```

#### 1.3 获取VIP分布数据
```
GET /big-r-overview/api/vip-distribution/
```

**响应示例：**
```json
{
    "success": true,
    "data": [
        {
            "level": "VIP10",
            "count": 45,
            "percentage": 5.1
        },
        {
            "level": "VIP9",
            "count": 78,
            "percentage": 8.7
        }
    ]
}
```

### 2. 详细分析API

#### 2.1 获取用户分类统计
```
GET /big-r-overview/api/user-classification/
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "total_users": {
            "total": 1247,
            "new_this_month": 65,
            "lost_this_month": 12
        },
        "potential_users": {
            "total": 892,
            "conversion_rate": 15.2,
            "top_potential": 156
        },
        "churn_risk": {
            "total": 23,
            "critical": 5,
            "high": 8,
            "medium": 10
        }
    }
}
```

#### 2.2 获取收入分析数据
```
GET /big-r-overview/api/revenue-analysis/?period=30
```

**响应示例：**
```json
{
    "success": true,
    "data": {
        "total_revenue": 3567890.50,
        "avg_arpu": 2847.50,
        "revenue_trend": [
            {"date": "2025-08-01", "revenue": 125000},
            {"date": "2025-08-02", "revenue": 132000}
        ],
        "top_spenders": [
            {"user_id": 12345, "amount": 50000, "vip_level": 10},
            {"user_id": 67890, "amount": 45000, "vip_level": 9}
        ]
    }
}
```

## 📊 数据同步机制

### 1. 数据源集成
```python
# 从游戏服务器同步用户数据
class GameDataSyncService:
    def sync_user_data(self):
        """同步用户基础数据"""
        pass

    def sync_recharge_data(self):
        """同步充值数据"""
        pass

    def sync_login_data(self):
        """同步登录数据"""
        pass
```

### 2. 数据处理流程
1. **数据采集**：从游戏服务器、支付系统采集原始数据
2. **数据清洗**：验证数据完整性，处理异常数据
3. **数据转换**：将原始数据转换为分析模型所需格式
4. **数据加载**：批量更新数据库表
5. **指标计算**：执行各种算法计算用户分类和指标
6. **缓存更新**：更新Redis缓存中的热点数据

### 3. 错误处理
- 数据同步失败重试机制
- 异常数据隔离和人工审核
- 关键指标计算失败告警
- 数据回滚和恢复机制

## 🔧 部署和维护

### 1. 环境要求
- **数据库**：PostgreSQL 13+ 或 MySQL 8.0+
- **缓存**：Redis 6.0+
- **任务队列**：Celery + Redis
- **监控**：Prometheus + Grafana

### 2. 初始化脚本
```python
# 初始化大R用户数据
python manage.py init_big_r_data

# 执行用户分类算法
python manage.py classify_users

# 生成历史统计数据
python manage.py generate_historical_stats
```

### 3. 定时任务配置
```python
# Celery定时任务配置
from celery.schedules import crontab

CELERY_BEAT_SCHEDULE = {
    'daily-user-classification': {
        'task': 'big_r_overview.tasks.classify_users',
        'schedule': crontab(hour=2, minute=0),  # 每日凌晨2点
    },
    'daily-stats-generation': {
        'task': 'big_r_overview.tasks.generate_daily_stats',
        'schedule': crontab(hour=3, minute=0),  # 每日凌晨3点
    },
    'weekly-behavior-analysis': {
        'task': 'big_r_overview.tasks.analyze_user_behavior',
        'schedule': crontab(hour=4, minute=0, day_of_week=0),  # 每周日凌晨4点
    },
}
```

## 📋 开发检查清单

### 1. 数据模型
- [ ] 创建BigRUser模型
- [ ] 创建UserRechargeRecord模型
- [ ] 创建UserLoginRecord模型
- [ ] 创建BigROverviewStats模型
- [ ] 创建UserBehaviorAnalysis模型
- [ ] 添加必要的数据库索引
- [ ] 编写数据模型测试

### 2. 业务逻辑
- [ ] 实现大R用户识别算法
- [ ] 实现潜力用户识别算法
- [ ] 实现流失风险识别算法
- [ ] 实现核心指标计算函数
- [ ] 实现趋势数据计算函数
- [ ] 编写业务逻辑测试

### 3. API接口
- [ ] 实现核心指标API
- [ ] 实现增长趋势API
- [ ] 实现VIP分布API
- [ ] 实现用户分类统计API
- [ ] 实现收入分析API
- [ ] 编写API接口测试

### 4. 数据同步
- [ ] 实现数据同步服务
- [ ] 配置定时任务
- [ ] 实现错误处理机制
- [ ] 配置监控告警
- [ ] 编写同步功能测试

### 5. 性能优化
- [ ] 数据库查询优化
- [ ] 缓存策略实现
- [ ] 批量处理优化
- [ ] 性能测试和调优

### 6. 部署配置
- [ ] 环境配置文件
- [ ] 数据库迁移脚本
- [ ] 初始化数据脚本
- [ ] 监控配置
- [ ] 文档完善

---

**文档版本**：v1.0
**创建日期**：2025-08-05
**最后更新**：2025-08-05
**维护人员**：开发团队
