#!/usr/bin/env python3
"""
MMO游戏大R用户维护系统 - 快速设置脚本
"""

import os
import sys
import subprocess


def run_command(command, description):
    """运行命令并显示描述"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False


def main():
    print("🚀 MMO游戏大R用户维护系统 - 快速设置")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查Django是否安装
    try:
        import django
        print(f"✅ Django版本: {django.get_version()}")
    except ImportError:
        print("❌ Django未安装，请先安装Django:")
        print("   pip install django")
        sys.exit(1)
    
    # 运行数据库迁移
    if not run_command("python manage.py makemigrations", "创建数据库迁移文件"):
        return False
    
    if not run_command("python manage.py migrate", "执行数据库迁移"):
        return False
    
    # 初始化示例数据
    if not run_command("python manage.py init_privilege_data", "初始化特权示例数据"):
        print("⚠️  初始化示例数据失败，但系统仍可正常使用")
    
    # 创建超级用户（可选）
    print("\n📝 是否创建管理员账户？(y/n): ", end="")
    create_superuser = input().lower().strip()
    
    if create_superuser in ['y', 'yes', '是']:
        print("请按提示创建管理员账户:")
        run_command("python manage.py createsuperuser", "创建管理员账户")
    
    print("\n🎉 设置完成！")
    print("\n📋 下一步操作:")
    print("1. 启动开发服务器: python manage.py runserver")
    print("2. 访问应用: http://127.0.0.1:8000")
    print("3. 访问管理后台: http://127.0.0.1:8000/admin")
    print("\n💡 提示:")
    print("- 特权管理页面: http://127.0.0.1:8000/privileges/")
    print("- 系统已初始化示例数据，可以直接体验功能")
    print("- 如需修改数据，可通过管理后台或API接口操作")


if __name__ == "__main__":
    main()
