# 故障排除指南

## 🔧 前后端连接问题

### 问题1: 页面显示加载中，但没有数据

**可能原因:**
- Django服务器未启动
- 数据库未迁移
- 示例数据未初始化
- JavaScript错误

**解决步骤:**

1. **检查Django服务器**
```bash
python manage.py runserver
```
确保服务器在 `http://127.0.0.1:8000` 运行

2. **检查数据库迁移**
```bash
python manage.py makemigrations
python manage.py migrate
```

3. **初始化示例数据**
```bash
python manage.py init_privilege_data
```

4. **测试API连接**
访问: `http://127.0.0.1:8000/privileges/api/debug/`
应该返回JSON格式的调试信息

5. **检查浏览器控制台**
按F12打开开发者工具，查看Console标签页是否有错误

### 问题2: 特权状态切换失败

**可能原因:**
- CSRF token问题
- API权限问题
- 数据库连接问题

**解决步骤:**

1. **检查CSRF token**
在浏览器控制台运行:
```javascript
console.log(document.querySelector('meta[name="csrf-token"]').content);
```

2. **测试API接口**
```bash
python test_api.py
```

3. **检查网络请求**
在浏览器开发者工具的Network标签页查看API请求状态

### 问题3: 数据库更改后前端不更新

**原因:** 前端缓存或需要刷新页面

**解决方法:**
1. 硬刷新页面 (Ctrl+F5)
2. 清除浏览器缓存
3. 检查API是否返回最新数据

## 🧪 测试工具

### 1. API测试脚本
```bash
python test_api.py
```

### 2. 调试API
访问: `http://127.0.0.1:8000/privileges/api/debug/`

### 3. 管理后台
访问: `http://127.0.0.1:8000/admin`
(需要先创建超级用户: `python manage.py createsuperuser`)

## 📋 常用命令

### 重置数据库
```bash
# 删除数据库文件
rm db.sqlite3

# 重新迁移
python manage.py migrate

# 重新初始化数据
python manage.py init_privilege_data
```

### 查看日志
```bash
# Django开发服务器会在终端显示请求日志
python manage.py runserver --verbosity=2
```

### 检查模型数据
```bash
python manage.py shell
```
然后在Python shell中:
```python
from privileges.models import Privilege
print(f"特权总数: {Privilege.objects.count()}")
for p in Privilege.objects.all():
    print(f"- {p.name}: {p.status}")
```

## 🐛 常见错误

### 1. `ModuleNotFoundError: No module named 'privileges'`
**解决:** 确保在Django项目根目录运行命令

### 2. `django.db.utils.OperationalError: no such table`
**解决:** 运行数据库迁移
```bash
python manage.py migrate
```

### 3. `CSRF verification failed`
**解决:** 检查CSRF token配置，确保meta标签正确设置

### 4. `404 Not Found` for API endpoints
**解决:** 检查URL配置，确保privileges应用已添加到INSTALLED_APPS

## 📞 获取帮助

如果问题仍然存在:

1. 检查Django和Python版本兼容性
2. 查看完整的错误堆栈信息
3. 确认所有依赖已正确安装
4. 检查防火墙和端口设置

## 🔍 调试技巧

### 前端调试
```javascript
// 在浏览器控制台中测试API
fetch('/privileges/api/list/')
  .then(response => response.json())
  .then(data => console.log(data));
```

### 后端调试
在Django views中添加调试信息:
```python
import logging
logger = logging.getLogger(__name__)
logger.debug(f"API调用: {request.method} {request.path}")
```
